package com.kyb.pcberp.modules.wechat.web.front;

import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.report.entity.ReportException;
import com.kyb.pcberp.modules.report.entity.ReportTwo;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.service.CockpitService;
import com.kyb.pcberp.modules.wechat.service.KybReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/** zjn 2019-08-01 微信报表 */
@Controller
@RequestMapping(value = "${frontPath}/wechat/kybReport/")
public class KybReportController
{
    @Autowired
    private KybReportService kybReportService;

    @Autowired
    private CockpitService cockpitService;
    
    // 获取销售报表数据
    @RequestMapping(value = "getSalesReportMain", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> getSalesReportMain(@RequestBody Report report, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybReportService.getSalesReportMain(report);
    }

    // 获取主页报表数据
    @RequestMapping(value = "getHomePageReport", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> getHomePageReport(@RequestBody Report report)
    {
        return kybReportService.getHomePageReport(report);
    }

    //获取天气时间
    @RequestMapping(value = "getAddress", method = RequestMethod.POST)
    @ResponseBody
    public WeatherInformation getAddress()
    {
        return cockpitService.getAddress();
    }

    //获取WIP数据汇总
    @RequestMapping(value = "getWipData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,Object>getWipData(@RequestBody Report report){
        return kybReportService.getWipData(report);
    }

    //获取部门总数据
    @RequestMapping(value = "getReportTotal", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,Object>getReportTotal(@RequestBody Report report){
        return kybReportService.getReportTotal(report);
    }

    //获取报表明细
    @RequestMapping(value = "getOrderDetail", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,Object>getOrderDetail(@RequestBody Report report){
        return kybReportService.getOrderDetail(report);
    }

    // 订单线/WIP
    @RequestMapping(value = "getOrderLineWipData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,Object> getOrderLineWipData(@RequestBody Report report)
    {
        return kybReportService.getOrderLineWipData(report);
    }
    //根据电话获取公司
    @RequestMapping(value = "getCompanyLists", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,List<?>> getCompanyLists (@RequestBody Company company)
    {
        return kybReportService.getCompanyLists(company);
    }
    // 微信进度
    @RequestMapping(value = "getProgress", method = RequestMethod.POST)
    @ResponseBody
    public ContractDetail getProgress (@RequestBody ContractDetail contractDetail)
    {
        return kybReportService.getProgress(contractDetail);
    }

    //获取员工职位
    @RequestMapping(value = "getEmployeeUserName", method = RequestMethod.POST)
    @ResponseBody
    public Employee getEmployeeUserName(@RequestBody Employee employee){
        return kybReportService.getEmployeeUserName(employee);
    }

    // WIP进度
    @RequestMapping(value = "getWipProgress", method = RequestMethod.POST)
    @ResponseBody
    public List<ProduceRecord> getWipProgress(@RequestBody ProduceRecord produceRecord)
    {
        return kybReportService.getWipProgress(produceRecord);
    }

    // 获取主页报表数据
    @RequestMapping(value = "getHomePageReportTwo", method = RequestMethod.POST)
    @ResponseBody
    public ReportTwo getHomePageReportTwo(@RequestBody Report report, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybReportService.getHomePageReportTwo(report,request,response);
    }

    // 获取异常弹窗数据
    @RequestMapping(value = "getShowDateData", method = RequestMethod.POST)
    @ResponseBody
    public List<Object> getShowDateData(@RequestBody ReportException reportException, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybReportService.getShowDateData(reportException,request,response);
    }

    // 获取订单统计状态数据
    @RequestMapping(value = "getOrderReportStatus", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> getOrderReportStatus(@RequestBody Report report)
    {
        return kybReportService.getOrderReportStatus(report);
    }
}
