package com.kyb.pcberp.modules.wechat.service;

import com.google.common.collect.Lists;
import com.google.zxing.oned.Code128Reader;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.sms.util.DateUtil;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.contract.service.MatPreparationService;
import com.kyb.pcberp.modules.crm.dao.RejectApplicationDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.eg.dao.CardADao;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.finance.dao.SingleReceivableDao;
import com.kyb.pcberp.modules.finance.dao.SingleReceivableDetailDao;
import com.kyb.pcberp.modules.finance.entity.CollectMuchMoney;
import com.kyb.pcberp.modules.finance.entity.SingleReceivableDetail;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_Item;
import com.kyb.pcberp.modules.production.dao.*;
import com.kyb.pcberp.modules.production.entity.*;
import com.kyb.pcberp.modules.production.service.PlanSchedulingService;
import com.kyb.pcberp.modules.production.service.ProduceRecordService;
import com.kyb.pcberp.modules.production.service.ProductionBottleneckService;
import com.kyb.pcberp.modules.purch.dao.PrdorderDetailDao;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDao;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.quality.dao.InspectDao;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.report.dao.ReportExceptionDao;
import com.kyb.pcberp.modules.report.dao.SalesReportDao;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.sys.dao.*;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsTwo;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.dao.WechatOaDao;
import com.kyb.pcberp.modules.wechat.utils.OrderRankingSystem;
import com.sun.org.apache.bcel.internal.generic.IF_ACMPEQ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

import java.rmi.server.ExportException;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class KybReportService
{
    @Autowired
    private SalesReportDao salesReportDao;

    @Autowired
    private PositionEmployeeDao positionEmployeeDao;

    @Autowired
    private CockpitSheetDao cockpitSheetDao;

    @Autowired
    private SingleReceivableDao singleReceivableDao;

    @Autowired
    private BatchDetailCapacityDao batchDetailCapacityDao;

    @Autowired
    private BottleneckProcessUseDao bottleneckProcessUseDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private PurchasingDetailDao purchasingDetailDao;

    @Autowired
    private CockpitDao cockpitDao;

    @Autowired
    private RejectApplicationDao rejectApplicationDao;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private SingleReceivableDetailDao singleReceivableDetailDao;

    @Autowired
    private InspectDao inspectDao;

    @Autowired
    private ProduceBatchDetailDao produceBatchDetailDao;

    @Autowired
    private ProduceRecordDao produceRecordDao;

    @Autowired
    private WechatOaDao wechatOaDao;

    @Autowired
    private ProduceRecordService produceRecordService;

    @Autowired
    private PlanSchedulingService planSchedulingService;

    @Autowired
    private ProductionBottleneckService productionBottleneckService;

    @Autowired
    private MatPreparationService matPreparationService;

    @Autowired
    private ReportExceptionDao reportExceptionDao;

    @Autowired
    private BatchDeliveryDao batchDeliveryDao;

    @Autowired
    private CapacityDao capacityDao;

    @Autowired
    private BottleneckProcessDao bottleneckProcessDao;

    @Autowired
    private PlanSchedulingDao planSchedulingDao;

    @Autowired
    private ContractDao contractDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private NotificationDao notificationDao;

    /**
     * fzd 2019-08-07 获取销售报表数据
     */
    public Map<String, Object> getSalesReportMain(Report report)
    {
        Map<String, Object> data = new HashMap<>();

        User user = new User(report.getUserId());
        user.setCompany(report.getCompany());
        List<PositionEmployee> peList = positionEmployeeDao.getListByUserId(user);
        if (Collections3.isNotEmpty(peList))
        {
            if (null != peList.get(0))
            {
                report.setStatusFlag(peList.get(0).getStatus());
                if ("2".equals(report.getStatusFlag()))
                {
                    report.setDeptId(peList.get(0).getGroupId());
                }
            }
        }
        if (StringUtils.isBlank(report.getStatusFlag()))
        {
            data.put("result", "fail");
            data.put("message", "您没有报表权限!");
            return data;
        }
        // 分析数
        if (null != report.getAnalyticalNum())
        {
            Integer size = report.getAnalyticalNum();
            String queryDateStrs = null;// 时间组
            for (int i = 0; i < size; i++)
            {
                Date date = DateUtils.parseDate(report.getQueryDate());
                switch (report.getDateType())
                {
                    // 日
                    case 1:
                        if (StringUtils.isNotBlank(queryDateStrs))
                        {
                            queryDateStrs = queryDateStrs + ",'" + DateUtils.getDateBefore(date, i, 1) + "'";
                        }
                        else
                        {
                            queryDateStrs = "'" + DateUtils.getDateBefore(date, i, 1) + "'";
                        }
                        break;
                    // 周
                    case 2:
                        if (StringUtils.isNotBlank(queryDateStrs))
                        {
                            queryDateStrs = queryDateStrs + "," + DateUtils.getDateBefore(date, (i + 1) * 7, 2);
                        }
                        else
                        {
                            queryDateStrs = DateUtils.getDateBefore(date, (i + 1) * 7, 2);
                        }
                        break;
                    // 月
                    case 3:
                        if (StringUtils.isNotBlank(queryDateStrs))
                        {
                            queryDateStrs = queryDateStrs + "," + DateUtils.getDateBefore(date, i, 3);
                        }
                        else
                        {
                            queryDateStrs = DateUtils.getDateBefore(date, i, 3);
                        }
                        break;
                }
            }
            report.setQueryDateStrs(queryDateStrs);
        }
        List<Report> reportList = salesReportDao.getSalesReportMain(report);
        data.put("reportList", reportList);

        // 销售
        if ("1".equals(report.getReportType()))
        {
            List<Report> overDueReportList = salesReportDao.getSalesReportOverDue(report);
            data.put("overDueReportList", overDueReportList);
        }

        data.put("result", "success");
        return data;
    }

    public Map<String, Object> getHomePageReport(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == report || StringUtils.isBlank(report.getUserId())) {
            data.put("message", "未获取到员工数据");
            return data;
        }
        // 二次鉴权
        List<Hr_Item> itemList = userDao.getItemListByUser(report.getUserId(), "4");
        if (itemList == null || itemList.size() == 0){
            data.put("message", "二次鉴权失败，拒绝加载报表");
            return data;
        }
        String dateQuery = StringUtils.isNotBlank(report.getDateQuery()) && report.getDateQuery().equals("2") ? "2" : "1";
        // 获取当日，本周，本月
        Map<String, String> timeMap = DateUtils.setTypeTime(report.getDateType(), dateQuery);
        String showDate = timeMap.get("showDate");
        String startTime = timeMap.get("startTime");
        String endTime = timeMap.get("endTime");
        if (StringUtils.isBlank(showDate)){
            data.put("message", "请选择正确的报表时间类型");
            return data;
        }
        data.put("showDate",showDate);
        report.setStartTime(startTime);
        report.setEndTime(endTime);

        BigDecimal orderAllArea = BigDecimal.ZERO;
        BigDecimal orderAllMoney = BigDecimal.ZERO;
        BigDecimal orderAllProfit = BigDecimal.ZERO;
        List<Cockpit> orderAllList = new ArrayList<>();
        Map<String, String> orderMap = new HashMap<>();

        BigDecimal saleAllArea = BigDecimal.ZERO;
        BigDecimal saleAllMoney = BigDecimal.ZERO;
        BigDecimal saleAllProfit = BigDecimal.ZERO;
        BigDecimal saleAllNum = BigDecimal.ZERO;
        List<Cockpit> saleAllList = new ArrayList<>();
        Map<String, String> saleMap = new HashMap<>();

        BigDecimal financeAllIn = BigDecimal.ZERO;
        BigDecimal financeAllOut = BigDecimal.ZERO;
        List<Cockpit> financeAllInList = new ArrayList<>();
        List<Cockpit> financeAllOutList = new ArrayList<>();
        Map<String, String> financeInMap = new HashMap<>();
        Map<String, String> financeOutMap = new HashMap<>();

        BigDecimal custAllQuatity = BigDecimal.ZERO;
        BigDecimal custAllNum = BigDecimal.ZERO;
        BigDecimal selfAllQuatity = BigDecimal.ZERO;
        BigDecimal addAllArea = BigDecimal.ZERO;
        List<Cockpit> custAllQuatityList = new ArrayList<>();
        List<Cockpit> selfAllQuatityList = new ArrayList<>();
        List<Cockpit> addAllAreaList = new ArrayList<>();
        Map<String, String> custQuatityMap = new HashMap<>();
        Map<String, String> selfQuatityMap = new HashMap<>();
        Map<String, String> addAreaMap = new HashMap<>();

        BigDecimal productAllNum = BigDecimal.ZERO;
        BigDecimal productAllMoney = BigDecimal.ZERO;
        List<Cockpit> productAllList = new ArrayList<>();
        Map<String, String> productMap = new HashMap<>();

        BigDecimal purchAllNum = BigDecimal.ZERO;
        BigDecimal purchAllMoney = BigDecimal.ZERO;
        List<Cockpit> purchAllList = new ArrayList<>();
        Map<String, String> purchMap = new HashMap<>();

        // 投料出库
        BigDecimal feedingOutAllArea = BigDecimal.ZERO;
        List<Cockpit> feedingOutAllList = new ArrayList<>();
        Map<String, String> feedingOutMap = new HashMap<>();

        // 生产入库
        BigDecimal productionStorageAllArea = BigDecimal.ZERO;
        List<Cockpit> productionStorageAllList = new ArrayList<>();
        Map<String, String> productionStorageMap = new HashMap<>();

        // 结存
        BigDecimal balanceAllArea = BigDecimal.ZERO;
        List<Cockpit> balanceAllList = new ArrayList<>();
        Map<String, String> balanceMap = new HashMap<>();

        for(Hr_Item item : itemList) {
            // 按权限分别查，js进行处理汇总
            if (StringUtils.isBlank(item.getName())){
                continue;
            }
            String erpId = StringUtils.isNotBlank(item.getErpId()) ? item.getErpId() : "";
            String departName = StringUtils.isNotBlank(item.getDepartName()) ? item.getDepartName() : "";
            report.setCompanyId(erpId);
            report.setDepartName(departName);
            // 查询数据
            if (item.getName().equals("接单")){
                // 后面从redis查
                List<Cockpit> orderList = cockpitDao.getOrderData(report);
                for (Cockpit cockpit: orderList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    BigDecimal workingAmount = cockpit.getWorkingAmount() != null ? cockpit.getWorkingAmount() : BigDecimal.ZERO;
                    BigDecimal netCostFee;
                    if (StringUtils.isNotBlank(cockpit.getDetailId()) && cockpit.getOrgId().equals(cockpit.getDetailId())){
                        // 对外数据处理
                        netCostFee = cockpit.getNetCostFee() != null ? cockpit.getNetCostFee() : BigDecimal.ZERO;
                    }else {
                        // 对内数据处理
                        netCostFee = workingAmount;
                    }
                    BigDecimal profit = workingAmount.subtract(netCostFee);
                    cockpit.setProfit(profit);
                    if (dealReport(orderMap, orderAllList, cockpit)){
                        orderAllArea = orderAllArea.add(workingArea);
                        orderAllMoney = orderAllMoney.add(workingAmount);
                        orderAllProfit = orderAllProfit.add(profit);
                    }
                }
            }else if (item.getName().equals("出货")){
                // 后面从redis查
                List<Cockpit> saleList = cockpitDao.getDeliverData(report);
                for (Cockpit cockpit: saleList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    BigDecimal workingAmount = cockpit.getWorkingAmount() != null ? cockpit.getWorkingAmount() : BigDecimal.ZERO;
                    BigDecimal netCostFee;
                    if (StringUtils.isNotBlank(cockpit.getDetailId()) && cockpit.getOrgId().equals(cockpit.getDetailId())){
                        // 对外数据处理
                        netCostFee = cockpit.getNetCostFee() != null ? cockpit.getNetCostFee() : BigDecimal.ZERO;
                    }else {
                        // 对内数据处理
                        netCostFee = workingAmount;
                    }
                    BigDecimal profit = workingAmount.subtract(netCostFee);
                    cockpit.setProfit(profit);
                    if (dealReport(saleMap, saleAllList, cockpit)){
                        saleAllArea = saleAllArea.add(workingArea);
                        saleAllMoney = saleAllMoney.add(workingAmount);
                        saleAllProfit = saleAllProfit.add(profit);
                        saleAllNum = saleAllNum.add(BigDecimal.ONE);
                    }
                }
            }else if (item.getName().equals("回款")){
                // 后面从redis查
                List<Cockpit> inList = cockpitDao.getSingleIn(report);
                for (Cockpit cockpit: inList){
                    BigDecimal sentGoodsValue = cockpit.getSentGoodsValue() != null ? cockpit.getSentGoodsValue() : BigDecimal.ZERO;
                    BigDecimal receivedAmount = cockpit.getReceivedAmount() != null ? cockpit.getReceivedAmount() : BigDecimal.ZERO;
                    BigDecimal waitFee = sentGoodsValue.subtract(receivedAmount);
                    cockpit.setWaitFee(waitFee);
                    if (dealReport(financeInMap, financeAllInList, cockpit)){
                        financeAllIn = financeAllIn.add(waitFee);
                    }
                }
                List<Cockpit> outList = cockpitDao.getSingleOut(report);
                for (Cockpit cockpit: outList){
                    BigDecimal recvMaterialValue = cockpit.getRecvMaterialValue() != null ? cockpit.getRecvMaterialValue() : BigDecimal.ZERO;
                    BigDecimal paiedAmount = cockpit.getPaiedAmount() != null ? cockpit.getPaiedAmount() : BigDecimal.ZERO;
                    BigDecimal waitFee = recvMaterialValue.subtract(paiedAmount);
                    cockpit.setWaitFee(waitFee);
                    if (dealReport(financeOutMap, financeAllOutList, cockpit)){
                        financeAllOut = financeAllOut.add(waitFee);
                    }
                }
            }else if (item.getName().equals("品质")){
                List<Cockpit> customerPlaintList = cockpitDao.getCustomerPlaint(report);
                for (Cockpit cockpit: customerPlaintList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(custQuatityMap, custAllQuatityList, cockpit)){
                        custAllQuatity = custAllQuatity.add(workingArea);
                        custAllNum = custAllNum.add(BigDecimal.ONE);
                    }
                }
                List<Cockpit> productScrapList = cockpitDao.getProductScrap(report);
                for (Cockpit cockpit: productScrapList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(selfQuatityMap, selfAllQuatityList, cockpit)){
                        selfAllQuatity = selfAllQuatity.add(workingArea);
                    }
                }
                List<Cockpit> replenishList = cockpitDao.getReplenish(report);
                for (Cockpit cockpit: replenishList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(addAreaMap, addAllAreaList, cockpit)){
                        addAllArea = addAllArea.add(workingArea);
                    }
                }
            }else if (item.getName().equals("采购")){
                List<Cockpit> productOrderList = cockpitDao.getProductOrder(report);
                for (Cockpit cockpit: productOrderList){
                    BigDecimal workingAmount = cockpit.getWorkingAmount() != null ? cockpit.getWorkingAmount() : BigDecimal.ZERO;
                    if (dealReport(productMap, productAllList, cockpit)){
                        productAllMoney = productAllMoney.add(workingAmount);
                        productAllNum = productAllNum.add(BigDecimal.ONE);
                    }
                }
                List<Cockpit> rawOrderList = cockpitDao.getRawOrder(report);
                for (Cockpit cockpit: rawOrderList){
                    BigDecimal workingAmount = cockpit.getWorkingAmount() != null ? cockpit.getWorkingAmount() : BigDecimal.ZERO;
                    if (dealReport(purchMap, purchAllList, cockpit)){
                        purchAllMoney = purchAllMoney.add(workingAmount);
                        purchAllNum = purchAllNum.add(BigDecimal.ONE);
                    }
                }
            }
            else if (item.getName().equals("生产")){
                // 投料出库
                List<Cockpit> feedingOutList = cockpitDao.getFeedingOutList(report);
                for (Cockpit cockpit: feedingOutList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(feedingOutMap, feedingOutAllList, cockpit)){
                        feedingOutAllArea = feedingOutAllArea.add(workingArea);
                    }
                }
                // 生产入库
                List<Cockpit> productionStorageList = cockpitDao.getProductionStorageList(report);
                for (Cockpit cockpit: productionStorageList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(productionStorageMap, productionStorageAllList, cockpit)){
                        productionStorageAllArea = productionStorageAllArea.add(workingArea);
                    }
                }
                // 结存
                List<Cockpit> balanceList = cockpitDao.getBalanceList(report);
                for (Cockpit cockpit: balanceList){
                    BigDecimal workingArea = cockpit.getWorkingArea() != null ? cockpit.getWorkingArea() : BigDecimal.ZERO;
                    if (dealReport(balanceMap, balanceAllList, cockpit)){
                        balanceAllArea = balanceAllArea.add(workingArea);
                    }
                }
            }
        }
        // 接单
        Map<String, String> orderSumMap = new HashMap<>();
        orderSumMap.put("1", "A");orderSumMap.put("2", "M");orderSumMap.put("3", "P");
        Map<String, String> orderRankMap = new HashMap<>();
        orderRankMap.put("1", "custRanking");orderRankMap.put("2", "departRanking");orderRankMap.put("3", "saleRanking");orderRankMap.put("4", "userRanking");
        data = setRankIng(data, orderAllList, orderRankMap, orderSumMap);
        data.put("orderAllArea", orderAllArea);
        data.put("orderAllMoney", orderAllMoney);
        data.put("orderAllProfit", orderAllProfit);
        // 销售
        Map<String, String> saleRankMap = new HashMap<>();
        saleRankMap.put("1", "saleCustRanking");saleRankMap.put("2", "saleDepartRanking");saleRankMap.put("3", "saleSaleRanking");saleRankMap.put("4", "saleUserRanking");
        data = setRankIng(data, saleAllList, saleRankMap, orderSumMap);
        data.put("saleAllArea", saleAllArea);
        data.put("saleAllMoney", saleAllMoney);
        data.put("saleAllProfit", saleAllProfit);
        data.put("saleAllNum", saleAllNum);
        // 财务
        Map<String, String> financeSumMap = new HashMap<>();
        financeSumMap.put("4", "W");
        Map<String, String> financeInRankMap = new HashMap<>();
        financeInRankMap.put("1", "financeCustRanking");financeInRankMap.put("2", "financeDepartRanking");
        financeInRankMap.put("5", "financeCompanyRanking");financeInRankMap.put("6", "financePeriodRanking");
        data = setRankIng(data, financeAllInList, financeInRankMap, financeSumMap);
        Map<String, String> financeOutRankMap = new HashMap<>();
        financeOutRankMap.put("1", "financeSupplierRanking");financeOutRankMap.put("5", "financeCompanySupRanking");financeOutRankMap.put("6", "financePeriodSupRanking");
        data = setRankIng(data, financeAllOutList, financeOutRankMap, financeSumMap);
        data.put("financeAllIn", financeAllIn);
        data.put("financeAllOut", financeAllOut);
        // 品质
        data.put("custAllQuatityRanking", OrderRankingSystem.setRank(custAllQuatityList, "7", "1"));
        data.put("selfAllQuatityRanking", OrderRankingSystem.setRank(selfAllQuatityList, "8", "1"));
        data.put("custAllQuatity", custAllQuatity);
        data.put("selfAllQuatity", selfAllQuatity);
        data.put("addAllArea", addAllArea);
        data.put("custAllNum", custAllNum);
        // 采购
        data.put("productAllRanking", OrderRankingSystem.setRank(productAllList, "9", "2"));
        data.put("purchAllRanking", OrderRankingSystem.setRank(purchAllList, "9", "2"));
        data.put("productAllComRanking", OrderRankingSystem.setRank(productAllList, "5", "2"));
        data.put("purchAllComRanking", OrderRankingSystem.setRank(purchAllList, "5", "2"));
        data.put("productAllMoney", productAllMoney);
        data.put("productAllNum", productAllNum);
        data.put("purchAllMoney", purchAllMoney);
        data.put("purchAllNum", purchAllNum);

        // 生产

        // 投料出库
//        Map<String, String> feedingOutSumMap = new HashMap<>();
//        feedingOutSumMap.put("1", "A");
//        Map<String, String> feedingOutRankMap = new HashMap<>();
//        feedingOutRankMap.put("1", "feedingOutCustRanking");feedingOutRankMap.put("2", "feedingOutDepartRanking");
//        feedingOutRankMap.put("3", "feedingOutSaleRanking");feedingOutRankMap.put("4", "feedingOutUserRanking");
//        data = setRankIng(data, feedingOutAllList, feedingOutRankMap, feedingOutSumMap);
        data.put("feedingOutAllArea", feedingOutAllArea);

        // 生产入库
//        Map<String, String> productionStorageRankMap = new HashMap<>();
//        productionStorageRankMap.put("1", "productionStorageCustRanking");productionStorageRankMap.put("2", "productionStorageDepartRanking");
//        productionStorageRankMap.put("3", "productionStorageSaleRanking");productionStorageRankMap.put("4", "productionStorageUserRanking");
//        data = setRankIng(data, productionStorageAllList, productionStorageRankMap, feedingOutSumMap);
        data.put("productionStorageAllArea", productionStorageAllArea);

        // 结存
//        Map<String, String> balanceRankMap = new HashMap<>();
//        balanceRankMap.put("1", "balanceCustRanking");balanceRankMap.put("2", "balanceDepartRanking");
//        balanceRankMap.put("3", "balanceSaleRanking");balanceRankMap.put("4", "balanceUserRanking");
//        data = setRankIng(data, balanceAllList, balanceRankMap, feedingOutSumMap);
        data.put("balanceAllArea", balanceAllArea);

        data.put("message", "success");
        return data;
    }

    public boolean dealReport(Map<String, String> map, List<Cockpit> list, Cockpit cockpit){
        // 去重处理
        if (map.containsKey(cockpit.getRecordId())){
            return false;
        }
        map.put(cockpit.getRecordId(), cockpit.getRecordId());
        list.add(cockpit);
        return true;
    }

    public Map<String, Object> setRankIng(Map<String, Object> data, List<Cockpit> list, Map<String, String> rankMap, Map<String, String> sumMap){
        for (Map.Entry<String, String> entry : rankMap.entrySet()){
            for (Map.Entry<String, String> entrySum : sumMap.entrySet()){
                data.put(entry.getValue() + entrySum.getValue(), OrderRankingSystem.setRank(list, entry.getKey(), entrySum.getKey()));
            }
        }
        return data;
    }

    // 获取生产数据
    public Map<String,Object> giveBirthData(Report report)
    {
        Map<String, Object> data = new HashMap<>();

        data.put("nowDate",report.getQueryDate()); // 当前日期

        Integer takeHand = planSchedulingService.getTakeHandSet();

        // 获取生产工序管理
        List<Cockpit> productionProcessList = cockpitDao.getProductionProcessData(report);

        // 获取瓶颈工序使用列表
        BottleneckProcessUse bottleneckProcessUse = new BottleneckProcessUse();
        bottleneckProcessUse.setCompanyId(report.getCompanyId());
        bottleneckProcessUse.setStartTime(report.getQueryDate());
        List<BottleneckProcessUse> useList = bottleneckProcessUseDao.getProcessUseList(bottleneckProcessUse);

        if(Collections3.isNotEmpty(productionProcessList))
        {
            for(Cockpit ck : productionProcessList)
            {
                Integer arrangementNum = 0; // 安排款数/任务款数
                BigDecimal arrangementArea = BigDecimal.ZERO; // 安排面积/任务面积
                Integer reachStandardNum = 0; // 达标款数
                BigDecimal reachStandardArea = BigDecimal.ZERO; // 达标面积
                Integer completedNum = 0; // 完成款数
                BigDecimal completedArea = BigDecimal.ZERO; // 完成面积

                if(Collections3.isNotEmpty(useList))
                {
                    for(BottleneckProcessUse use : useList)
                    {
                        if(report.getQueryDate().equals(use.getOccurrenceDateStr()))
                        {
                            // 瓶颈工序id一致
                            if(ck.getProcessId().equals(use.getProcessManagementId()))
                            {
                                if(StringUtils.isBlank(ck.getProductionType()) || (StringUtils.isNotBlank(ck.getProductionType())
                                    && StringUtils.isNotBlank(use.getProductionType()) && ck.getProductionType().equals(use.getProductionType())))
                                {
                                    arrangementNum++;
                                    arrangementArea = arrangementArea.add(use.getUseArea());

                                    if(takeHand == 1)
                                    {
                                        if(null != use.getNextProcessTime() || "4".equals(use.getHandOverFlag()))
                                        {
                                            if(null != use.getOccurrenceDate())
                                            {
                                                Date nextProcessTime = use.getNextProcessTime();
                                                if("4".equals(use.getHandOverFlag()))
                                                {
                                                    nextProcessTime = use.getHandOverTime();
                                                }
                                                completedNum++;
                                                completedArea = completedArea.add(use.getUseArea());


                                                if(DateUtils.formatDate(use.getOccurrenceDate()).compareTo(DateUtils.formatDate(nextProcessTime)) >= 0)
                                                {
                                                    reachStandardNum++;
                                                    reachStandardArea = reachStandardArea.add(use.getUseArea());
                                                }
                                            }
                                        }
                                    }
                                    else if(takeHand == 2 && null != use.getHandOverTime())
                                    {
                                        completedNum++;
                                        completedArea = completedArea.add(use.getUseArea());
                                        Date handOverTime = use.getHandOverTime();
                                        if(DateUtils.formatDate(use.getOccurrenceDate()).compareTo(DateUtils.formatDate(handOverTime)) >= 0)
                                        {
                                            reachStandardNum++;
                                            reachStandardArea = reachStandardArea.add(use.getUseArea());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                ck.setArrangementNum(arrangementNum);
                ck.setArrangementArea(setScale(arrangementArea,2));

                ck.setReachStandardNum(reachStandardNum);
                ck.setReachStandardArea(setScale(reachStandardArea,2));

                ck.setCompletedNum(completedNum);
                ck.setCompletedArea(setScale(completedArea,2));

                // 达成率
                Integer dividend = arrangementNum == 0 ? 1 : arrangementNum;
                BigDecimal reachStandardRate = new BigDecimal(reachStandardNum).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(dividend),2, BigDecimal.ROUND_HALF_UP);
                ck.setReachStandardRate(reachStandardRate);
            }
        }
        data.put("productionProcessList",productionProcessList);

        return data;
    }

    // 获取采购数据
    public Map<String,Object> getPurchaseData(Report report)
    {
        Map<String, Object> data = new HashMap<>();

        // 获取采购数据
        report.setDateFlag("1"); // 当前
        List<Cockpit> rawMaterialPurchaseList =  cockpitDao.getRawMaterialPurchaseData(report);

        report.setDateFlag("2"); // 上一个
        List<Cockpit> beforeRawMaterialPurchaseList =  cockpitDao.getRawMaterialPurchaseData(report);

        Map<String,GroupOrgRelation> map = new HashMap<>();
        if(Collections3.isNotEmpty(rawMaterialPurchaseList))
        {
            Integer sumCount = 0;
            BigDecimal sumArea = BigDecimal.ZERO;
            BigDecimal sumAmount = BigDecimal.ZERO;
            BigDecimal targetedPurchasingAmount = BigDecimal.ZERO; // 定向采购金额
            BigDecimal comparisonOrderAmount = BigDecimal.ZERO; // 比价下单金额
            BigDecimal inquiryOrderAmount = BigDecimal.ZERO; // 询价下单金额
            for(Cockpit ck : rawMaterialPurchaseList)
            {
                if(null == ck.getWorkingArea())
                {
                    ck.setWorkingArea(BigDecimal.ZERO);
                }
                if(null == ck.getWorkingAmount())
                {
                    ck.setWorkingAmount(BigDecimal.ZERO);
                }
                sumCount++;
                sumArea = sumArea.add(ck.getWorkingArea());
                sumAmount = sumAmount.add(ck.getWorkingAmount());

                GroupOrgRelation gr = new GroupOrgRelation();
                if(map.containsKey(ck.getManufacturer()))
                {
                    gr = map.get(ck.getManufacturer());
                }
                if(null == gr.getCount())
                {
                    gr.setCount(0);
                }
                if(null == gr.getArea())
                {
                    gr.setArea(BigDecimal.ZERO);
                }
                if(null == gr.getAmount())
                {
                    gr.setAmount(BigDecimal.ZERO);
                }
                if(null == gr.getTargetedPurchasingAmount())
                {
                    gr.setTargetedPurchasingAmount(BigDecimal.ZERO);
                }
                if(null == gr.getComparisonOrderAmount())
                {
                    gr.setComparisonOrderAmount(BigDecimal.ZERO);
                }
                if(null == gr.getInquiryOrderAmount())
                {
                    gr.setInquiryOrderAmount(BigDecimal.ZERO);
                }
                gr.setCount(gr.getCount() + 1);
                gr.setArea(gr.getArea().add(ck.getWorkingArea()));
                gr.setAmount(gr.getAmount().add(ck.getWorkingAmount()));
                switch (ck.getPurchaseWay())
                {
                    // 询价下单
                    case "1":
                        inquiryOrderAmount = inquiryOrderAmount.add(ck.getWorkingAmount());
                        gr.setInquiryOrderAmount(gr.getInquiryOrderAmount().add(ck.getWorkingAmount()));
                        break;
                    // 比价下单
                    case "2":
                        comparisonOrderAmount = comparisonOrderAmount.add(ck.getWorkingAmount());
                        gr.setComparisonOrderAmount(gr.getComparisonOrderAmount().add(ck.getWorkingAmount()));
                        break;
                    // 定向采购
                    case "3":
                        targetedPurchasingAmount = targetedPurchasingAmount.add(ck.getWorkingAmount());
                        gr.setTargetedPurchasingAmount(gr.getTargetedPurchasingAmount().add(ck.getWorkingAmount()));
                        break;
                }
                map.put(ck.getManufacturer(),gr);
            }
            data.put("sumCount",sumCount);

            data.put("sumArea",setScale(sumArea,2));
            data.put("sumAmount",setScale(sumAmount,2));
            data.put("inquiryOrderAmount",setScale(inquiryOrderAmount,2));
            data.put("comparisonOrderAmount",setScale(comparisonOrderAmount,2));
            data.put("targetedPurchasingAmount",setScale(targetedPurchasingAmount,2));
        }

        // 获取备料数据
        List<Cockpit> preparationList = cockpitDao.getPreparationData(report);

        // 获取报备使用列表
        List<Cockpit> reportUseList = cockpitDao.getReportUseData(report);

        // 获取上一天，上一周，上一月
        String beforeShowDate = null;
        String showDate = null;
        if(null != report.getDateType())
        {
            Calendar c = Calendar.getInstance();
            c.setTime(DateUtils.parseDate(report.getQueryDate()));
            switch (report.getDateType())
            {
                // 日
                case 1:
                    showDate = DateUtils.formatDate(c.getTime());
                    c.add(Calendar.DATE, - 1);
                    beforeShowDate = DateUtils.formatDate(c.getTime());
                    break;
                // 周
                case 2:
                    int weekOfYear = c.get(Calendar.WEEK_OF_YEAR);
                    showDate = DateUtils.formatDate(c.getTime(),"yyyy") + weekOfYear;
                    c.add(Calendar.DATE, - 7);
                    weekOfYear = c.get(Calendar.WEEK_OF_YEAR);
                    beforeShowDate = DateUtils.formatDate(c.getTime(),"yyyy") + weekOfYear;
                    break;
                // 月
                case 3:
                    showDate = DateUtils.formatDate(c.getTime(),"yyyyMM");
                    c.add(Calendar.MONTH, - 1);
                    beforeShowDate = DateUtils.formatDate(c.getTime(),"yyyyMM");
                    break;
            }
        }

        List<GroupOrgRelation> groupDeptList = report.getGroupDeptList();
        if(Collections3.isNotEmpty(groupDeptList))
        {
            for (GroupOrgRelation gr : groupDeptList)
            {
                BigDecimal area = BigDecimal.ZERO; // 备料面积
                BigDecimal useArea = BigDecimal.ZERO; // 已使用面积
                BigDecimal waitUseArea = BigDecimal.ZERO; // 待使用面积

                BigDecimal beforeArea = BigDecimal.ZERO; // 上月备料面积
                BigDecimal beforeUseArea = BigDecimal.ZERO; // 上月已使用面积
                BigDecimal beforeWaitUseArea = BigDecimal.ZERO; // 上月待使用面积

                BigDecimal totalArea = BigDecimal.ZERO; // 累计备料面积
                BigDecimal totalUseArea = BigDecimal.ZERO; // 累计已使用面积
                BigDecimal totalWaitUseArea = BigDecimal.ZERO; // 累计待使用面积

                if(Collections3.isNotEmpty(preparationList))
                {
                    for(Cockpit preparation : preparationList)
                    {
                        if(null == preparation.getWorkingArea() || !preparation.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            continue;
                        }
                        if(showDate.equals(preparation.getShowDate()))
                        {
                            area = area.add(preparation.getWorkingArea());
                        }
                        else if(beforeShowDate.equals(preparation.getShowDate()))
                        {
                            beforeArea = beforeArea.add(preparation.getWorkingArea());
                        }
                        totalArea = totalArea.add(preparation.getWorkingArea());
                    }
                }
                if(Collections3.isNotEmpty(reportUseList))
                {
                    for(Cockpit reportUse : reportUseList)
                    {
                        if(null == reportUse.getWorkingArea() || !reportUse.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            continue;
                        }
                        if(showDate.equals(reportUse.getShowDate()))
                        {
                            useArea = useArea.add(reportUse.getWorkingArea());
                        }
                        else if(beforeShowDate.equals(reportUse.getShowDate()))
                        {
                            beforeUseArea = beforeUseArea.add(reportUse.getWorkingArea());
                        }
                        totalUseArea = totalUseArea.add(reportUse.getWorkingArea());
                    }
                }
                waitUseArea = area.subtract(useArea);
                beforeWaitUseArea = beforeArea.subtract(beforeUseArea);
                totalWaitUseArea = totalArea.subtract(totalUseArea);

                gr.setArea(setScale(area,2));
                gr.setUseArea(setScale(useArea,2));
                gr.setWaitUseArea(setScale(waitUseArea,2));

                gr.setTotalArea(setScale(totalArea,2));
                gr.setTotalUseArea(setScale(totalUseArea,2));
                gr.setTotalWaitUseArea(setScale(totalWaitUseArea,2));

                // 获取上升/下降的备料面积/已用面积/待用面积
                BigDecimal areaRate = getBigDecimalRate(area,beforeArea);
                BigDecimal useAreaRate = getBigDecimalRate(useArea,beforeUseArea);
                BigDecimal waitUseAreaRate = getBigDecimalRate(waitUseArea,beforeWaitUseArea);
                Boolean areaRateFlag = getBooleanFlag(area,beforeArea);
                Boolean useAreaRateFlag = getBooleanFlag(useArea,beforeUseArea);
                Boolean waitUseAreaRateFlag = getBooleanFlag(waitUseArea,beforeWaitUseArea);

                gr.setAreaRate(areaRate); // 备料面积率
                gr.setUseAreaRate(useAreaRate); // 已用面积率
                gr.setWaitUseAreaRate(waitUseAreaRate); // 待用面积率
                gr.setAreaRateFlag(areaRateFlag); // 备料面积率上升/下降标志
                gr.setUseAreaRateFlag(useAreaRateFlag); // 已用面积率上升/下降标志
                gr.setWaitUseAreaRateFlag(waitUseAreaRateFlag); // 待用面积率上升/下降标志
            }
        }
        data.put("groupDeptList",groupDeptList);

        // 根据物料获取map集合
        List<Material> matList = Lists.newArrayList();
        Map<String,String> matMap = new HashMap<>();
        for(Cockpit ck : rawMaterialPurchaseList)
        {
            if(null == ck.getMaterial() || StringUtils.isBlank(ck.getMaterial().getRecordId()))
            {
                continue;
            }
            String key = ck.getMaterial().getRecordId();
            if(matMap.containsKey(key))
            {
               continue;
            }
            matMap.put(key,key);
            matList.add(ck.getMaterial());
        }
        // 处理物料数据
        if(Collections3.isNotEmpty(matList))
        {
            RawStockUtil rawUtil = new RawStockUtil();
            matList = rawUtil.getMaterialList(matList, null);
            for(Material mat : matList)
            {
                if(StringUtils.isNotBlank(mat.getBordLength()) && StringUtils.isNotBlank(mat.getBordWidth()) && null != mat.getStocks())
                {
                    BigDecimal area = new BigDecimal(mat.getBordLength()).multiply(new BigDecimal(mat.getBordWidth())).multiply(mat.getStocks()).divide(new BigDecimal(1000000),2,BigDecimal.ROUND_HALF_UP);
                    mat.setArea(area);
                }
            }
            matList.sort((x, y) -> -((null == x.getStocks() ? BigDecimal.ZERO : x.getStocks()).compareTo(null == y.getStocks() ? BigDecimal.ZERO : y.getStocks())));
        }
        data.put("matList",matList);

        List<GroupOrgRelation> manufacturerList = Lists.newArrayList();
        if(null != map)
        {
            for (Map.Entry<String, GroupOrgRelation> entry : map.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    GroupOrgRelation gr = entry.getValue();
                    gr.setManufacturer(entry.getKey());
                    Integer count = null == gr.getCount() ? 0 : gr.getCount();
                    BigDecimal area = null == gr.getArea() ? BigDecimal.ZERO : gr.getArea();
                    BigDecimal amount = null == gr.getAmount() ? BigDecimal.ZERO : gr.getAmount();

                    Integer beforeCount = 0; // 上月款数
                    BigDecimal beforeArea = BigDecimal.ZERO; // 上月面积
                    BigDecimal beforeAmount = BigDecimal.ZERO; // 上月金额
                    if(Collections3.isNotEmpty(beforeRawMaterialPurchaseList))
                    {
                        for(Cockpit before : beforeRawMaterialPurchaseList)
                        {
                            if(entry.getKey().equals(before.getManufacturer()))
                            {
                                if(null == before.getWorkingArea())
                                {
                                    before.setWorkingArea(BigDecimal.ZERO);
                                }
                                if(null == before.getWorkingAmount())
                                {
                                    before.setWorkingAmount(BigDecimal.ZERO);
                                }
                                beforeCount++;
                                beforeArea = beforeArea.add(before.getWorkingArea());
                                beforeAmount = beforeAmount.add(before.getWorkingAmount());
                            }
                        }
                    }
                    // 获取上升/下降的款数、金额、面积
                    BigDecimal countRate = getBigDecimalRate(new BigDecimal(count),new BigDecimal(beforeCount));
                    BigDecimal areaRate = getBigDecimalRate(area,beforeArea);
                    BigDecimal amountRate = getBigDecimalRate(amount,beforeAmount);
                    Boolean countRateFlag = getBooleanFlag(new BigDecimal(count),new BigDecimal(beforeCount));
                    Boolean areaRateFlag = getBooleanFlag(area,beforeArea);
                    Boolean amountRateFlag = getBooleanFlag(amount,beforeAmount);
                    gr.setCountRate(countRate); // 款数率
                    gr.setAreaRate(areaRate); // 面积率
                    gr.setAmountRate(amountRate); // 金额率
                    gr.setCountRateFlag(countRateFlag); // 款数率上升/下降标志
                    gr.setAreaRateFlag(areaRateFlag); // 面积率上升/下降标志
                    gr.setAmountRateFlag(amountRateFlag); // 金额率上升/下降标志
                    manufacturerList.add(gr);
                }
            }
        }
        if(Collections3.isNotEmpty(manufacturerList))
        {
            manufacturerList.sort((x, y) -> -((null == x.getArea() ? BigDecimal.ZERO : x.getArea()).compareTo(null == y.getArea() ? BigDecimal.ZERO : y.getArea())));
        }
        data.put("manufacturerList",manufacturerList);
        return data;
    }

    // 获取品质数据
    public Map<String,Object> getQualityData(Report report)
    {
        Map<String, Object> data = new HashMap<>();

        // 获取客诉数据
        report.setDateFlag("1");// 当前
        List<Cockpit> rejectList = cockpitDao.getRejectData(report);

        Integer count = 0;
        BigDecimal complaintArea = BigDecimal.ZERO; // 客诉面积
        BigDecimal fedArea = BigDecimal.ZERO; // 补发面积
        BigDecimal returnAmount = BigDecimal.ZERO; // 退货金额
        BigDecimal compensationAmount = BigDecimal.ZERO; // 赔偿金额
        BigDecimal fedAmount = BigDecimal.ZERO; // 补发金额
        if (Collections3.isNotEmpty(rejectList))
        {
            for (Cockpit cockpit : rejectList)
            {
                count++;
                if (null != cockpit.getComplaintArea())
                {
                    complaintArea = complaintArea.add(cockpit.getComplaintArea());
                }
                if (null != cockpit.getFedArea())
                {
                    fedArea = fedArea.add(cockpit.getFedArea());
                }
                if (null != cockpit.getReturnAmount())
                {
                    returnAmount = returnAmount.add(cockpit.getReturnAmount());
                }
                if (null != cockpit.getCompensationAmount())
                {
                    compensationAmount = compensationAmount.add(cockpit.getCompensationAmount());
                }
                if (null != cockpit.getFedAmount())
                {
                    fedAmount = fedAmount.add(cockpit.getFedAmount());
                }
            }
            data.put("count",count);
            data.put("complaintArea",setScale(complaintArea,2));
            data.put("fedArea",setScale(fedArea,2));
            data.put("returnAmount",setScale(returnAmount,2));
            data.put("compensationAmount",setScale(compensationAmount,2));
            data.put("fedAmount",setScale(fedAmount,2));
        }

        // 获取补料面积
        report.setDateFlag("1");// 当前
        List<Cockpit> supplementaryFeedingList = cockpitDao.getSupplementaryFeedingData(report);

        report.setDateFlag("2");// 上一个
        List<Cockpit> beforeSupplementaryFeedingList = cockpitDao.getSupplementaryFeedingData(report);

        // 获取不良面积
        report.setDateFlag("1");// 当前
        List<Cockpit> badnessList = cockpitDao.getBadnessData(report);

        report.setDateFlag("2");// 上一个
        List<Cockpit> beforeBadnessList = cockpitDao.getBadnessData(report);

        // 获取返工面积
        report.setDateFlag("1");// 当前
        List<Cockpit> reworkList = cockpitDao.getReworkData(report);

        report.setDateFlag("2");// 上一个
        List<Cockpit> beforeReworkList = cockpitDao.getReworkData(report);

        // 获取报废面积
        report.setDateFlag("1");// 当前
        List<Cockpit> scrapList = cockpitDao.getScrapData(report);

        report.setDateFlag("2");// 上一个
        List<Cockpit> beforeScrapList = cockpitDao.getScrapData(report);

        // 创建map存储当前几种数据
        Map<String,List<Cockpit>> nowMap = new HashMap<>();
        nowMap.put("1",supplementaryFeedingList); // 补料
        nowMap.put("2",badnessList); // 不良
        nowMap.put("3",reworkList); // 返工
        nowMap.put("4",scrapList); // 报废
        // 创建map存储上一个几种数据
        Map<String,List<Cockpit>> beforeMap = new HashMap<>();
        beforeMap.put("1",beforeSupplementaryFeedingList); // 补料
        beforeMap.put("2",beforeBadnessList); // 不良
        beforeMap.put("3",beforeReworkList); // 返工
        beforeMap.put("4",beforeScrapList); // 报废

        // 累计数值
        BigDecimal sumSupplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
        BigDecimal sumBadnessArea = BigDecimal.ZERO; // 不良面积
        BigDecimal sumReworkArea = BigDecimal.ZERO; // 返工面积
        BigDecimal sumScrapArea = BigDecimal.ZERO; // 报废面积

        // 处理部门分组数据并统计数据
        List<GroupOrgRelation> groupDeptList = report.getGroupDeptList();
        if (Collections3.isNotEmpty(groupDeptList))
        {
            for (GroupOrgRelation gr : groupDeptList)
            {
                // 当前
                BigDecimal supplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                BigDecimal badnessArea = BigDecimal.ZERO; // 不良面积
                BigDecimal reworkArea = BigDecimal.ZERO; // 返工面积
                BigDecimal scrapArea = BigDecimal.ZERO; // 报废面积
                // 处理当前数据
                if(null != nowMap)
                {
                    for (Map.Entry<String, List<Cockpit>> entry : nowMap.entrySet())
                    {
                        if (entry != null && entry.getValue() != null)
                        {
                            List<Cockpit> valueList = entry.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(StringUtils.isBlank(value.getGroupOrgId()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entry.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            supplementaryFeedingArea = supplementaryFeedingArea.add(area);
                                            sumSupplementaryFeedingArea = sumSupplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            badnessArea = badnessArea.add(area);
                                            sumBadnessArea = sumBadnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            reworkArea = reworkArea.add(area);
                                            sumReworkArea = sumReworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            scrapArea = scrapArea.add(area);
                                            sumScrapArea = sumScrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }

                // 上一个
                BigDecimal beforeSupplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                BigDecimal beforeBadnessArea = BigDecimal.ZERO; // 不良面积
                BigDecimal beforeReworkArea = BigDecimal.ZERO; // 返工面积
                BigDecimal beforeScrapArea = BigDecimal.ZERO; // 报废面积

                // 处理上一个数据
                if(null != beforeMap)
                {
                    for (Map.Entry<String, List<Cockpit>> entry : beforeMap.entrySet())
                    {
                        if (entry != null && entry.getValue() != null)
                        {
                            List<Cockpit> valueList = entry.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(StringUtils.isBlank(value.getGroupOrgId()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entry.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            beforeSupplementaryFeedingArea = beforeSupplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            beforeBadnessArea = beforeBadnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            beforeReworkArea = beforeReworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getGroupOrgId().equals(gr.getGroupOrgId()))
                                        {
                                            beforeScrapArea = beforeScrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }

                gr.setSupplementaryFeedingArea(setScale(supplementaryFeedingArea,2)); // 补料面积
                gr.setBadnessArea(setScale(badnessArea,2)); // 不良面积
                gr.setReworkArea(setScale(reworkArea,2)); // 返工面积
                gr.setScrapArea(setScale(scrapArea,2)); // 补料面积

                // 获取上升/下降的面积
                BigDecimal supplementaryFeedingAreaRate = getBigDecimalRate(supplementaryFeedingArea,beforeSupplementaryFeedingArea); // 补料
                Boolean supplementaryFeedingAreaFlag = getBooleanFlag(supplementaryFeedingArea,beforeSupplementaryFeedingArea);
                BigDecimal badnessAreaRate = getBigDecimalRate(badnessArea,beforeBadnessArea); // 不良
                Boolean badnessAreaFlag = getBooleanFlag(badnessArea,beforeBadnessArea);
                BigDecimal reworkAreaRate = getBigDecimalRate(reworkArea,beforeReworkArea); // 返工
                Boolean reworkAreaFlag = getBooleanFlag(reworkArea,beforeReworkArea);
                BigDecimal scrapAreaRate = getBigDecimalRate(scrapArea,beforeScrapArea); // 报废
                Boolean scrapAreaFlag = getBooleanFlag(scrapArea,beforeScrapArea);
                gr.setSupplementaryFeedingAreaRate(supplementaryFeedingAreaRate);
                gr.setSupplementaryFeedingAreaFlag(supplementaryFeedingAreaFlag);
                gr.setBadnessAreaRate(badnessAreaRate);
                gr.setBadnessAreaFlag(badnessAreaFlag);
                gr.setReworkAreaRate(reworkAreaRate);
                gr.setReworkAreaFlag(reworkAreaFlag);
                gr.setScrapAreaRate(scrapAreaRate);
                gr.setScrapAreaFlag(scrapAreaFlag);
            }
        }
        // 排序根据报废面积累计值倒序
        if(Collections3.isNotEmpty(groupDeptList))
        {
            groupDeptList.sort((x, y) -> -((null == x.getScrapArea() ? BigDecimal.ZERO : x.getScrapArea())
                .compareTo(null == y.getScrapArea() ? BigDecimal.ZERO : y.getScrapArea())));
        }
        data.put("groupDeptList",groupDeptList);
        data.put("sumBadnessArea",sumBadnessArea);
        data.put("sumScrapArea",sumScrapArea);
        data.put("sumReworkArea",sumReworkArea);
        data.put("sumSupplementaryFeedingArea",sumSupplementaryFeedingArea);

        // 分组map
        Map<String,GroupOrgRelation> causeMap = new HashMap<>(); // 原因
        Map<String,Customer> customerMap = new HashMap<>();// 客户
        if(null != nowMap)
        {
            for (Map.Entry<String, List<Cockpit>> entry : nowMap.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    List<Cockpit> valueList = entry.getValue();
                    for (Cockpit value : valueList)
                    {
                        // 加入原因
                        String causeKey = value.getDiscardCause();
                        if(StringUtils.isNotBlank(causeKey))
                        {
                            // 不包含加入
                            if(!causeMap.containsKey(causeKey))
                            {
                                GroupOrgRelation gr = new GroupOrgRelation();
                                gr.setManufacturer(causeKey); // 原因
                                causeMap.put(causeKey,gr);
                            }
                        }
                        // 加入客户
                        String customerKey = null == value.getCustomer() ? null : value.getCustomer().getRecordId();
                        if(StringUtils.isNotBlank(customerKey))
                        {
                            // 不包含加入
                            if(!causeMap.containsKey(customerMap))
                            {
                                Customer cus = new Customer();
                                cus.setRecordId(customerKey); // 客户
                                cus.setShortName(value.getCustomer().getShortName());
                                customerMap.put(customerKey,cus);
                            }
                        }
                    }
                }
            }
        }
        // 原因数据处理
        List<GroupOrgRelation> causeList = Lists.newArrayList();
        if(null != causeMap)
        {
            for (Map.Entry<String, GroupOrgRelation> entry : causeMap.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    GroupOrgRelation gr = entry.getValue();

                    // 当前
                    BigDecimal supplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                    BigDecimal badnessArea = BigDecimal.ZERO; // 不良面积
                    BigDecimal reworkArea = BigDecimal.ZERO; // 返工面积
                    BigDecimal scrapArea = BigDecimal.ZERO; // 报废面积

                    if(null != nowMap)
                    {
                        for (Map.Entry<String, List<Cockpit>> entryTwo : nowMap.entrySet())
                        {
                            List<Cockpit> valueList = entryTwo.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(StringUtils.isBlank(value.getDiscardCause()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entryTwo.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            supplementaryFeedingArea = supplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            badnessArea = badnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            reworkArea = reworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            scrapArea = scrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }

                    // 上一个
                    BigDecimal beforeSupplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                    BigDecimal beforeBadnessArea = BigDecimal.ZERO; // 不良面积
                    BigDecimal beforeReworkArea = BigDecimal.ZERO; // 返工面积
                    BigDecimal beforeScrapArea = BigDecimal.ZERO; // 报废面积

                    if(null != beforeMap)
                    {
                        for (Map.Entry<String, List<Cockpit>> entryTwo : beforeMap.entrySet())
                        {
                            List<Cockpit> valueList = entryTwo.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(StringUtils.isBlank(value.getDiscardCause()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entryTwo.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            beforeSupplementaryFeedingArea = beforeSupplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            beforeBadnessArea = beforeBadnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            beforeReworkArea = beforeReworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getDiscardCause().equals(gr.getManufacturer()))
                                        {
                                            beforeScrapArea = beforeScrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }

                    gr.setSupplementaryFeedingArea(setScale(supplementaryFeedingArea,2)); // 补料面积
                    gr.setBadnessArea(setScale(badnessArea,2)); // 不良面积
                    gr.setReworkArea(setScale(reworkArea,2)); // 返工面积
                    gr.setScrapArea(setScale(scrapArea,2)); // 补料面积

                    // 获取上升/下降的面积
                    BigDecimal supplementaryFeedingAreaRate = getBigDecimalRate(supplementaryFeedingArea,beforeSupplementaryFeedingArea); // 补料
                    Boolean supplementaryFeedingAreaFlag = getBooleanFlag(supplementaryFeedingArea,beforeSupplementaryFeedingArea);
                    BigDecimal badnessAreaRate = getBigDecimalRate(badnessArea,beforeBadnessArea); // 不良
                    Boolean badnessAreaFlag = getBooleanFlag(badnessArea,beforeBadnessArea);
                    BigDecimal reworkAreaRate = getBigDecimalRate(reworkArea,beforeReworkArea); // 返工
                    Boolean reworkAreaFlag = getBooleanFlag(reworkArea,beforeReworkArea);
                    BigDecimal scrapAreaRate = getBigDecimalRate(scrapArea,beforeScrapArea); // 报废
                    Boolean scrapAreaFlag = getBooleanFlag(scrapArea,beforeScrapArea);
                    gr.setSupplementaryFeedingAreaRate(supplementaryFeedingAreaRate);
                    gr.setSupplementaryFeedingAreaFlag(supplementaryFeedingAreaFlag);
                    gr.setBadnessAreaRate(badnessAreaRate);
                    gr.setBadnessAreaFlag(badnessAreaFlag);
                    gr.setReworkAreaRate(reworkAreaRate);
                    gr.setReworkAreaFlag(reworkAreaFlag);
                    gr.setScrapAreaRate(scrapAreaRate);
                    gr.setScrapAreaFlag(scrapAreaFlag);
                    causeList.add(gr);
                }
            }
        }
        // 排序根据报废面积累计值倒序
        if(Collections3.isNotEmpty(causeList))
        {
            causeList.sort((x, y) -> -((null == x.getScrapArea() ? BigDecimal.ZERO : x.getScrapArea())
                .compareTo(null == y.getScrapArea() ? BigDecimal.ZERO : y.getScrapArea())));
        }
        data.put("causeList",causeList);

        // 客户数据处理
        List<Customer> customerList = Lists.newArrayList();
        if(null != customerMap)
        {
            for (Map.Entry<String, Customer> entry : customerMap.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    Customer cus = entry.getValue();

                    // 当前
                    BigDecimal supplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                    BigDecimal badnessArea = BigDecimal.ZERO; // 不良面积
                    BigDecimal reworkArea = BigDecimal.ZERO; // 返工面积
                    BigDecimal scrapArea = BigDecimal.ZERO; // 报废面积

                    if(null != nowMap)
                    {
                        for (Map.Entry<String, List<Cockpit>> entryTwo : nowMap.entrySet())
                        {
                            List<Cockpit> valueList = entryTwo.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(null == value.getCustomer() || StringUtils.isBlank(value.getCustomer().getRecordId()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entryTwo.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            supplementaryFeedingArea = supplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            badnessArea = badnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            reworkArea = reworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            scrapArea = scrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }

                    // 上一个
                    BigDecimal beforeSupplementaryFeedingArea = BigDecimal.ZERO; // 补料面积
                    BigDecimal beforeBadnessArea = BigDecimal.ZERO; // 不良面积
                    BigDecimal beforeReworkArea = BigDecimal.ZERO; // 返工面积
                    BigDecimal beforeScrapArea = BigDecimal.ZERO; // 报废面积

                    if(null != beforeMap)
                    {
                        for (Map.Entry<String, List<Cockpit>> entryTwo : beforeMap.entrySet())
                        {
                            List<Cockpit> valueList = entryTwo.getValue();
                            for(Cockpit value : valueList)
                            {
                                if(null == value.getCustomer() || StringUtils.isBlank(value.getCustomer().getRecordId()))
                                {
                                    continue;
                                }
                                BigDecimal area = null == value.getWorkingArea() ? BigDecimal.ZERO : value.getWorkingArea();

                                // 处理统计面积和部门面积
                                switch (entryTwo.getKey())
                                {
                                    // 补料
                                    case "1":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            beforeSupplementaryFeedingArea = beforeSupplementaryFeedingArea.add(area);
                                        }
                                        break;
                                    // 不良
                                    case "2":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            beforeBadnessArea = beforeBadnessArea.add(area);
                                        }
                                        break;
                                    // 返工
                                    case "3":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            beforeReworkArea = beforeReworkArea.add(area);
                                        }
                                        break;
                                    // 报废
                                    case "4":
                                        if(value.getCustomer().getRecordId().equals(cus.getRecordId()))
                                        {
                                            beforeScrapArea = beforeScrapArea.add(area);
                                        }
                                        break;
                                }
                            }
                        }
                    }

                    cus.setSupplementaryFeedingArea(setScale(supplementaryFeedingArea,2)); // 补料面积
                    cus.setBadnessArea(setScale(badnessArea,2)); // 不良面积
                    cus.setReworkArea(setScale(reworkArea,2)); // 返工面积
                    cus.setScrapArea(setScale(scrapArea,2)); // 补料面积

                    // 获取上升/下降的面积
                    BigDecimal supplementaryFeedingAreaRate = getBigDecimalRate(supplementaryFeedingArea,beforeSupplementaryFeedingArea); // 补料
                    Boolean supplementaryFeedingAreaFlag = getBooleanFlag(supplementaryFeedingArea,beforeSupplementaryFeedingArea);
                    BigDecimal badnessAreaRate = getBigDecimalRate(badnessArea,beforeBadnessArea); // 不良
                    Boolean badnessAreaFlag = getBooleanFlag(badnessArea,beforeBadnessArea);
                    BigDecimal reworkAreaRate = getBigDecimalRate(reworkArea,beforeReworkArea); // 返工
                    Boolean reworkAreaFlag = getBooleanFlag(reworkArea,beforeReworkArea);
                    BigDecimal scrapAreaRate = getBigDecimalRate(scrapArea,beforeScrapArea); // 报废
                    Boolean scrapAreaFlag = getBooleanFlag(scrapArea,beforeScrapArea);
                    cus.setSupplementaryFeedingAreaRate(supplementaryFeedingAreaRate);
                    cus.setSupplementaryFeedingAreaFlag(supplementaryFeedingAreaFlag);
                    cus.setBadnessAreaRate(badnessAreaRate);
                    cus.setBadnessAreaFlag(badnessAreaFlag);
                    cus.setReworkAreaRate(reworkAreaRate);
                    cus.setReworkAreaFlag(reworkAreaFlag);
                    cus.setScrapAreaRate(scrapAreaRate);
                    cus.setScrapAreaFlag(scrapAreaFlag);
                    customerList.add(cus);
                }
            }
        }
        // 排序根据报废面积累计值倒序
        if(Collections3.isNotEmpty(customerList))
        {
            customerList.sort((x, y) -> -((null == x.getScrapArea() ? BigDecimal.ZERO : x.getScrapArea())
                .compareTo(null == y.getScrapArea() ? BigDecimal.ZERO : y.getScrapArea())));
        }
        data.put("customerList",customerList);
        return data;
    }

    // 获取入库数据
    public Map<String,Object> getStorageData(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        // 入库列表
        report.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_IN);
        report.setDateFlag("1");// 当前
        List<Cockpit> storageList = cockpitDao.getStoreData(report);
        getWarehousingStatisticsDate(data,storageList,"1",report.getCompareDateFlag());

        // 送货出库列表
        report.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_OUT);
        report.setDateFlag("1");// 当前
        List<Cockpit> deliveryOutList = cockpitDao.getStoreData(report);
        getWarehousingStatisticsDate(data,deliveryOutList,"2",report.getCompareDateFlag());

        // 尾数面积
        BigDecimal mantissaArea = cockpitDao.getSumMantissaArea(report);
        data.put("mantissaArea",mantissaArea);

        // 获取部门的面积、金额、款数
        List<GroupOrgRelation> groupDeptList = report.getGroupDeptList();
        if(Collections3.isNotEmpty(groupDeptList))
        {
            for(GroupOrgRelation gr : groupDeptList)
            {
                BigDecimal sumArea = BigDecimal.ZERO; //面积
                BigDecimal sumSampleArea = BigDecimal.ZERO; //样品面积
                BigDecimal sumProductionArea = BigDecimal.ZERO; //量产面积
                Integer sumCount = 0; //款数
                Integer sumSampleCount = 0; //样品款数
                Integer sumProductionCount = 0; //量产款数

                BigDecimal deliveryDelay = BigDecimal.ZERO; //送货延期
                BigDecimal storedDelivery = BigDecimal.ZERO; //入库未送

                Integer deliveryNormalCount = 0; //送货正常款数

                if(Collections3.isNotEmpty(storageList))
                {
                    for (Cockpit ck : storageList)
                    {
                        if (null == ck)
                        {
                            continue;
                        }
                        if(null == ck.getWorkingArea())
                        {
                            ck.setWorkingArea(BigDecimal.ZERO);
                        }
                        if(StringUtils.isNotBlank(ck.getGroupOrgId())  && ck.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            sumCount++;
                            sumArea = sumArea.add(ck.getWorkingArea());
                            if(StringUtils.isNotBlank(ck.getOrderType()) && ck.getOrderType().equals("1"))
                            {
                                sumSampleArea = sumSampleArea.add(ck.getWorkingArea());
                                sumSampleCount++;
                            }
                            if(StringUtils.isNotBlank(ck.getOrderType()) && ck.getOrderType().equals("2"))
                            {
                                sumProductionArea = sumProductionArea.add(ck.getWorkingArea());
                                sumProductionCount++;
                            }
                            //入库未送
                            if (StringUtils.isNotBlank(ck.getStatus()) && !ck.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_CHECKED) && !ck.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED))
                            {
                                storedDelivery = storedDelivery.add(ck.getWorkingArea());
                            }
                        }
                    }
                }

                if(Collections3.isNotEmpty(deliveryOutList))
                {
                    for (Cockpit ckTwo : deliveryOutList)
                    {
                        Boolean postponeFlag = false; // 送货延期标志
                        Boolean normalFlag = false; //正常标志
                        if (null == ckTwo)
                        {
                            continue;
                        }
                        if(null == ckTwo.getWorkingArea())
                        {
                            ckTwo.setWorkingArea(BigDecimal.ZERO);
                        }
                        if(StringUtils.isNotBlank(ckTwo.getGroupOrgId())  && ckTwo.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            sumCount++;
                            sumArea = sumArea.add(ckTwo.getWorkingArea());
                            if(StringUtils.isNotBlank(ckTwo.getOrderType()) && ckTwo.getOrderType().equals("1"))
                            {
                                sumSampleArea = sumSampleArea.add(ckTwo.getWorkingArea());
                                sumSampleCount++;
                            }
                            if(StringUtils.isNotBlank(ckTwo.getOrderType()) && ckTwo.getOrderType().equals("2"))
                            {
                                sumProductionArea = sumProductionArea.add(ckTwo.getWorkingArea());
                                sumProductionCount++;
                            }

                            if(StringUtils.isBlank(report.getCompareDateFlag()) || "1".equals(report.getCompareDateFlag()))
                            {
                                // 最终交期
                                if(null != ckTwo.getDeliveryDate())
                                {
                                    String deliveryDateStr = DateUtils.formatDate(ckTwo.getDeliveryDate());
                                    String operateDateStr = DateUtils.formatDate(ckTwo.getOperateDate());
                                    // 如果交货日期小于实际交货时间标志为延期
                                    if(operateDateStr.compareTo(deliveryDateStr) > 0)
                                    {
                                        postponeFlag = true;
                                    }else if(operateDateStr.compareTo(deliveryDateStr) < 0)
                                    {
                                        normalFlag = true;
                                    }
                                }
                            }
                            else if("2".equals(report.getCompareDateFlag()))
                            {
                                // 首批交期
                                if(null != ckTwo.getFirstBatchDate())
                                {
                                    String deliveryDateStr = DateUtils.formatDate(ckTwo.getFirstBatchDate());
                                    String operateDateStr = DateUtils.formatDate(ckTwo.getOperateDate());
                                    // 如果交货日期小于当前日期标志为延期
                                    if(operateDateStr.compareTo(deliveryDateStr) > 0)
                                    {
                                        postponeFlag = true;
                                    }else if(operateDateStr.compareTo(deliveryDateStr) < 0)
                                    {
                                        normalFlag = true;
                                    }
                                }
                            }
                            if(postponeFlag)
                            {
                                deliveryDelay = deliveryDelay.add(ckTwo.getWorkingArea());
                            }
                            if (normalFlag)
                            {
                                deliveryNormalCount++;
                            }

                        }
                    }
                }
                BigDecimal deliveryDateRate = new BigDecimal(deliveryNormalCount).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(deliveryOutList.size()),2,BigDecimal.ROUND_HALF_UP);

                gr.setDeliveryDateRate(deliveryDateRate); // 交货达成率
                gr.setSumArea(setScale(sumArea,2)); // 面积
                gr.setSumSampleArea(setScale(sumSampleArea,2)); //样品面积
                gr.setSumProductionArea(setScale(sumProductionArea,2)); // 量产面积
                gr.setSumCount(sumCount); // 款数
                gr.setSumSampleCount(sumSampleCount); //样品款数
                gr.setSumProductionCount(sumProductionCount); //量产款数
                gr.setStoredDelivery(storedDelivery); //入库未送
                gr.setDeliveryDelay(deliveryDelay); //送货延期

            }
        }
        if(Collections3.isNotEmpty(groupDeptList))
        {
            groupDeptList.sort((x, y) -> -((null == x.getSumArea() ? BigDecimal.ZERO : x.getSumArea())
                    .compareTo(null == y.getSumArea() ? BigDecimal.ZERO : y.getSumArea())));
        }
        data.put("groupDeptList",groupDeptList);

        // 根据客户获取map集合
        Map<String,List<Cockpit>> map = new HashMap<>();
        for(Cockpit ck : storageList)
        {
            if(null == ck.getCustomer() || StringUtils.isBlank(ck.getCustomer().getRecordId()))
            {
                continue;
            }
            String key = ck.getCustomer().getRecordId();
            List<Cockpit> list = Lists.newArrayList();
            if(map.containsKey(key))
            {
                list = map.get(key);
            }
            list.add(ck);
            map.put(key,list);
        }
        for(Cockpit ckTwo : deliveryOutList)
        {
            if(null == ckTwo.getCustomer() || StringUtils.isBlank(ckTwo.getCustomer().getRecordId()))
            {
                continue;
            }
            String key = ckTwo.getCustomer().getRecordId();
            List<Cockpit> list = Lists.newArrayList();
            if(map.containsKey(key))
            {
                list = map.get(key);
            }
            list.add(ckTwo);
            map.put(key,list);
        }
        // 获取客户数据
        List<Customer> customerList = Lists.newArrayList();
        if(null != map && map.size() > 0)
        {
            for (Map.Entry<String, List<Cockpit>> entry : map.entrySet())
            {
                Customer customer = new Customer();
                customer.setRecordId(entry.getKey());
                if (entry != null && entry.getValue() != null)
                {
                    List<Cockpit> list = entry.getValue();
                    if(StringUtils.isBlank(customer.getShortName()) && null != list.get(0).getCustomer())
                    {
                        customer.setShortName(list.get(0).getCustomer().getShortName());
                    }

                    BigDecimal sumArea = BigDecimal.ZERO; //面积
                    BigDecimal sumSampleArea = BigDecimal.ZERO; //样品面积
                    BigDecimal sumProductionArea = BigDecimal.ZERO; //量产面积
                    Integer sumCount = 0; //款数
                    Integer sumSampleCount = 0; //样品款数
                    Integer sumProductionCount = 0; //量产款数

                    BigDecimal deliveryDelay = BigDecimal.ZERO; //送货延期
                    BigDecimal storedDelivery = BigDecimal.ZERO; //入库未送

                    Integer deliveryNormalCount = 0; //送货正常款数
                    if(Collections3.isNotEmpty(storageList))
                    {
                        for (Cockpit ck : storageList)
                        {
                            if (ck == null)
                            {
                                continue;
                            }
                            if(null == ck.getWorkingArea())
                            {
                                ck.setWorkingArea(BigDecimal.ZERO);
                            }
                            if(null != ck.getCustomer() && StringUtils.isNotBlank(ck.getCustomer().getRecordId())  && ck.getCustomer().getRecordId().equals(customer.getRecordId()))
                            {
                                sumCount++;
                                sumArea = sumArea.add(ck.getWorkingArea());
                                if (StringUtils.isNotBlank(ck.getOrderType()) && ck.getOrderType().equals("1"))
                                {
                                    sumSampleArea = sumSampleArea.add(ck.getWorkingArea());
                                    sumSampleCount++;
                                }else if (StringUtils.isNotBlank(ck.getOrderType()) && ck.getOrderType().equals("2"))
                                {
                                    sumProductionArea = sumProductionArea.add(ck.getWorkingArea());
                                    sumProductionCount++;
                                }
                                //入库未送
                                if (StringUtils.isNotBlank(ck.getStatus()) && !ck.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_CHECKED) && !ck.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED))
                                {
                                    storedDelivery = storedDelivery.add(ck.getWorkingArea());
                                }
                            }
                        }
                    }
                    if(Collections3.isNotEmpty(deliveryOutList))
                    {
                        for (Cockpit ckTwo : deliveryOutList)
                        {
                            Boolean postponeFlag = false; //延期送货标志
                            Boolean normalFlag = false; //正常标志
                            if (null == ckTwo)
                            {
                                continue;
                            }
                            if (null == ckTwo.getWorkingArea())
                            {
                                ckTwo.setWorkingArea(BigDecimal.ZERO);
                            }
                            if(null != ckTwo.getCustomer() && StringUtils.isNotBlank(ckTwo.getCustomer().getRecordId())  && ckTwo.getCustomer().getRecordId().equals(customer.getRecordId()))
                            {
                                sumCount++;
                                sumArea = sumArea.add(ckTwo.getWorkingArea());
                                if (StringUtils.isNotBlank(ckTwo.getOrderType()) && ckTwo.getOrderType().equals("1"))
                                {
                                    sumSampleArea = sumSampleArea.add(ckTwo.getWorkingArea());
                                    sumSampleCount++;
                                }else if (StringUtils.isNotBlank(ckTwo.getOrderType()) && ckTwo.getOrderType().equals("2"))
                                {
                                    sumProductionArea = sumProductionArea.add(ckTwo.getWorkingArea());
                                    sumProductionCount++;
                                }

                                if(StringUtils.isBlank(report.getCompareDateFlag()) || "1".equals(report.getCompareDateFlag()))
                                {
                                    // 最终交期
                                    if(null != ckTwo.getDeliveryDate())
                                    {
                                        String deliveryDateStr = DateUtils.formatDate(ckTwo.getDeliveryDate());
                                        String operateDateStr = DateUtils.formatDate(ckTwo.getOperateDate());
                                        // 如果交货日期小于实际交货时间标志为延期
                                        if(operateDateStr.compareTo(deliveryDateStr) > 0)
                                        {
                                            postponeFlag = true;
                                        }else if(operateDateStr.compareTo(deliveryDateStr) < 0)
                                        {
                                            normalFlag = true;
                                        }
                                    }
                                }
                                else if("2".equals(report.getCompareDateFlag()))
                                {
                                    // 首批交期
                                    if(null != ckTwo.getFirstBatchDate())
                                    {
                                        String deliveryDateStr = DateUtils.formatDate(ckTwo.getFirstBatchDate());
                                        String operateDateStr = DateUtils.formatDate(ckTwo.getOperateDate());
                                        // 如果交货日期小于当前日期标志为延期
                                        if(operateDateStr.compareTo(deliveryDateStr) > 0)
                                        {
                                            postponeFlag = true;
                                        }else if(operateDateStr.compareTo(deliveryDateStr) < 0)
                                        {
                                            normalFlag = true;
                                        }
                                    }
                                }
                                if(postponeFlag)
                                {
                                    deliveryDelay = deliveryDelay.add(ckTwo.getWorkingArea());
                                }
                                if (normalFlag)
                                {
                                    deliveryNormalCount++;
                                }

                            }
                        }
                    }
                    BigDecimal deliveryDateRate = new BigDecimal(deliveryNormalCount).multiply(new BigDecimal(100))
                            .divide(new BigDecimal(deliveryOutList.size()),2,BigDecimal.ROUND_HALF_UP);

                    customer.setDeliveryDateRate(deliveryDateRate); // 交货达成率
                    customer.setSumArea(setScale(sumArea,2)); // 面积
                    customer.setSumSampleArea(setScale(sumSampleArea,2)); //样品面积
                    customer.setSumProductionArea(setScale(sumProductionArea,2)); // 量产面积
                    customer.setSumCount(sumCount); // 款数
                    customer.setSumSampleCount(sumSampleCount); //样品款数
                    customer.setSumProductionCount(sumProductionCount); //量产款数

                    customer.setStoredDelivery(storedDelivery); //入库未送
                    customer.setDeliveryDelay(deliveryDelay); //送货延期

                }
                customerList.add(customer);
            }
        }
        if(Collections3.isNotEmpty(customerList))
        {
            customerList.sort((x, y) -> -((null == x.getSumArea() ? BigDecimal.ZERO : x.getSumArea())
                    .compareTo(null == y.getSumArea() ? BigDecimal.ZERO : y.getSumArea())));
        }
        data.put("customerList",customerList);
        return data;
    }

    //获取正常、风险、延期集合
    public void getWarehousingStatisticsDate(Map<String, Object> data,List<Cockpit> list,String number,String compareDateFlag)
    {
        if(Collections3.isEmpty(list))
        {
            return;
        }
        Date nowDate = new Date();
        String dateStr = DateUtils.formatDate(nowDate);
        //表示统计入库款数、面积
        if (number.equals("1"))
        {
            BigDecimal sumStorageArea = BigDecimal.ZERO; //入库面积
            BigDecimal sumSampleAreaStorage = BigDecimal.ZERO; //入库样品面积
            BigDecimal sumStorageProductionArea = BigDecimal.ZERO; //入库量产面积
            Integer sumStorageCount = 0; //入库款数
            Integer sumSampleStorageCount = 0; //入库样品款数
            Integer sumStorageProductionCount = 0; //入库量产款数

            BigDecimal storageNotDelivery = BigDecimal.ZERO; //已入库待送货

            BigDecimal storageNormalArea = BigDecimal.ZERO;
            BigDecimal storagePostponeArea = BigDecimal.ZERO;
            BigDecimal storageRiskArea = BigDecimal.ZERO;
            for(Cockpit storage : list)
            {
                Boolean postponeFlag = false; // 延期标志
                Boolean riskFlag = false; // 风险标志
                if(null == storage)
                {
                    continue;
                }
                if(null == storage.getWorkingArea())
                {
                    storage.setWorkingArea(BigDecimal.ZERO);
                }
                sumStorageCount++; //入库款数
                sumStorageArea = sumStorageArea.add(storage.getWorkingArea()); //入库面积
                //统计样品
                if (StringUtils.isNotBlank(storage.getOrderType()) && storage.getOrderType().equals("1"))
                {
                    sumSampleAreaStorage = sumSampleAreaStorage.add(storage.getWorkingArea());
                    sumSampleStorageCount++;
                //统计量产
                }else if (StringUtils.isNotBlank(storage.getOrderType()) && storage.getOrderType().equals("2"))
                {
                    sumStorageProductionArea = sumStorageProductionArea.add(storage.getWorkingArea());
                    sumStorageProductionCount++;
                }

                //已入库待送货
                if (StringUtils.isNotBlank(storage.getStatus()) && !storage.getStatus().equals("200204") && !storage.getStatus().equals("200210"))
                {
                    storageNotDelivery = storageNotDelivery.add(storage.getWorkingArea());
                }


                if(null != storage.getProductionStorageDate())
                {
                    String productionStorageDateStr = DateUtils.formatDate(storage.getProductionStorageDate());
                    String operateDateStr = DateUtils.formatDate(storage.getOperateDate());
                    // 如果评估入库日期小于生产入库日期标志为延期
                    if(operateDateStr.compareTo(productionStorageDateStr) > 0)
                    {
                        postponeFlag = true;
                    }
                    if(!postponeFlag)
                    {
                        // 未下料就判断<3天
                        Date changeDate = DateUtils.dateAdd(storage.getProductionStorageDate(),-3);
                        String changeDateStr =  DateUtils.formatDate(changeDate);
                        if(operateDateStr.compareTo(changeDateStr) > 0)
                        {
                            riskFlag = true;
                        }
                    }
                }
                if(postponeFlag)
                {
                    storagePostponeArea = storagePostponeArea.add(storage.getWorkingArea());
                }
                else if(riskFlag)
                {
                    storageRiskArea = storageRiskArea.add(storage.getWorkingArea());
                }
                else
                {
                    storageNormalArea = storageNormalArea.add(storage.getWorkingArea());
                }
            }
            data.put("sumStorageArea",setScale(sumStorageArea,2)); //入库面积
            data.put("sumSampleAreaStorage",setScale(sumSampleAreaStorage,2)); // 入库样品面积
            data.put("sumStorageProductionArea",setScale(sumStorageProductionArea,2)); //入库量产面积
            data.put("sumStorageCount",sumStorageCount); //入库款数
            data.put("sumSampleStorageCount",sumSampleStorageCount); //入库样品款数
            data.put("sumStorageProductionCount",sumStorageProductionCount); //入库量产款数

            data.put("storageNotDelivery",storageNotDelivery); //已入库待送货

            data.put("storageNormalArea",storageNormalArea); //入库正常
            data.put("storageRiskArea",storageRiskArea); //入库风险
            data.put("storagePostponeArea",storagePostponeArea); //入库延期


        }else if (number.equals("2"))
        {
            BigDecimal sumDeliveryArea = BigDecimal.ZERO; //送货面积
            BigDecimal sumDeliverySampleArea = BigDecimal.ZERO; //送货样品面积
            BigDecimal sumDeliveryProductionArea = BigDecimal.ZERO; //送货量产面积
            Integer sumDeliveryCount = 0; //送货款数
            Integer sumDeliverySampleCount = 0; //送货样品款数
            Integer sumDeliveryProductionCount = 0; //送货量产款数

            BigDecimal deliveryNormalArea = BigDecimal.ZERO;
            BigDecimal deliveryPostponeArea = BigDecimal.ZERO;
            BigDecimal deliveryRiskArea = BigDecimal.ZERO;
            for(Cockpit delivery : list)
            {
                Boolean postponeFlag = false; // 延期标志
                Boolean riskFlag = false; // 风险标志
                if(null == delivery)
                {
                    continue;
                }
                if(null == delivery.getWorkingArea())
                {
                    delivery.setWorkingArea(BigDecimal.ZERO);
                }
                sumDeliveryCount++; //送货款数
                sumDeliveryArea = sumDeliveryArea.add(delivery.getWorkingArea()); //送货面积
                //统计送货样品
                if (StringUtils.isNotBlank(delivery.getOrderType()) && delivery.getOrderType().equals("1"))
                {
                    sumDeliverySampleArea = sumDeliverySampleArea.add(delivery.getWorkingArea());
                    sumDeliverySampleCount++;
                //统计送货量产
                }else if (StringUtils.isNotBlank(delivery.getOrderType()) && delivery.getOrderType().equals("2"))
                {
                    sumDeliveryProductionArea = sumDeliveryProductionArea.add(delivery.getWorkingArea());
                    sumDeliveryProductionCount++;
                }

                if(StringUtils.isBlank(compareDateFlag) || "1".equals(compareDateFlag))
                {
                    // 最终交期
                    if(null != delivery.getDeliveryDate())
                    {
                        String deliveryDateStr = DateUtils.formatDate(delivery.getDeliveryDate());
                        String operateDateStr = DateUtils.formatDate(delivery.getOperateDate());
                        // 如果交货日期小于实际交货时间标志为延期
                        if(operateDateStr.compareTo(deliveryDateStr) > 0)
                        {
                            postponeFlag = true;
                        }
                        if(!postponeFlag)
                        {
                            // 未下料就判断<3天
                            Date changeDate = DateUtils.dateAdd(delivery.getDeliveryDate(),-3);
                            String changeDateStr =  DateUtils.formatDate(changeDate);
                            if(operateDateStr.compareTo(changeDateStr) > 0)
                            {
                                riskFlag = true;
                            }
                        }
                    }
                }
                else if("2".equals(compareDateFlag))
                {
                    // 首批交期
                    if(null != delivery.getFirstBatchDate())
                    {
                        String deliveryDateStr = DateUtils.formatDate(delivery.getFirstBatchDate());
                        String operateDateStr = DateUtils.formatDate(delivery.getOperateDate());
                        // 如果交货日期小于当前日期标志为延期
                        if(operateDateStr.compareTo(deliveryDateStr) > 0)
                        {
                            postponeFlag = true;
                        }
                        if(!postponeFlag)
                        {
                            // 未下料就判断<3天
                            Date changeDate = DateUtils.dateAdd(delivery.getFirstBatchDate(),-3);
                            String changeDateStr =  DateUtils.formatDate(changeDate);
                            if(operateDateStr.compareTo(changeDateStr) > 0)
                            {
                                riskFlag = true;
                            }
                        }
                    }
                }
                if(postponeFlag)
                {
                    deliveryPostponeArea = deliveryPostponeArea.add(delivery.getWorkingArea());
                }
                else if(riskFlag)
                {
                    deliveryRiskArea = deliveryRiskArea.add(delivery.getWorkingArea());
                }
                else
                {
                    deliveryNormalArea = deliveryNormalArea.add(delivery.getWorkingArea());
                }

            }
            data.put("sumDeliveryArea",setScale(sumDeliveryArea,2)); //送货面积
            data.put("sumDeliverySampleArea",setScale(sumDeliverySampleArea,2)); //送货样品面积
            data.put("sumDeliveryProductionArea",setScale(sumDeliveryProductionArea,2)); //送货量产面积
            data.put("sumDeliveryCount",sumDeliveryCount); //送货款数
            data.put("sumDeliverySampleCount",sumDeliverySampleCount); //送货样品款数
            data.put("sumDeliveryProductionCount",sumDeliveryProductionCount); //送货量产款数

            data.put("deliveryNormalArea",deliveryNormalArea); //入库正常
            data.put("deliveryRiskArea",deliveryRiskArea); //入库风险
            data.put("deliveryPostponeArea",deliveryPostponeArea); //入库延期
        }
    }

    public Boolean getBooleanFlag(BigDecimal one,BigDecimal two)
    {
        Boolean flag = true;
        if(null == one || null == two)
        {
            if(null == one)
            {
                flag = false;
            }
            return flag;
        }
        if(one.compareTo(two) >= 0)
        {
            flag = true;
        }
        else
        {
            flag = false;
        }
        return flag;
    }

    public BigDecimal getBigDecimalRate(BigDecimal one,BigDecimal two)
    {
        BigDecimal rate = BigDecimal.ZERO;
        if(null == one || null == two)
        {
            return rate;
        }
        BigDecimal differenceValue = one.subtract(two);
        if(differenceValue.compareTo(BigDecimal.ZERO) < 0)
        {
            differenceValue = differenceValue.multiply(new BigDecimal(-1));
        }
        if(two.compareTo(BigDecimal.ZERO) == 0)
        {
            two = BigDecimal.ONE;
        }
        rate = differenceValue.multiply(new BigDecimal(100)).divide(two,2, BigDecimal.ROUND_HALF_UP);
        if(rate.compareTo(new BigDecimal(100)) > 0)
        {
            rate = new BigDecimal(100);
        }
        return rate;
    }

    // 获取接单数据
    public Map<String,Object> getOrderTakingData(Report report)
    {
        Map<String, Object> data = new HashMap<>();

        report.setDateFlag("1");// 当前
        List<Cockpit> orderList = cockpitDao.getOrderData(report);

        report.setDateFlag("2");// 上一个
        List<Cockpit> beforeOrderList = cockpitDao.getOrderData(report);

        List<Cockpit> deliveredList = Lists.newArrayList();
        if(Collections3.isNotEmpty(orderList))
        {
            List<String> list = Lists.newArrayList();
            for(Cockpit ck : orderList)
            {
                if(null != ck.getDetailStatus() && (ck.getDetailStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    || ck.getDetailStatus().equals(TypeKey.SL_CONTRACT_STATUS_CHECKED.toString())))
                {
                    list.add(ck.getDetailId());
                }
            }
            if(Collections3.isNotEmpty(list))
            {
                // 获取已交货订单
                deliveredList = cockpitDao.getDeliveredList(list,report.getCompareDateFlag());
                if(Collections3.isNotEmpty(deliveredList))
                {
                    Integer completedPayment = 0;
                    for(Cockpit delivered : deliveredList)
                    {
                        for(Cockpit ck : orderList)
                        {
                            if(delivered.getDetailId().equals(ck.getDetailId()))
                            {
                                // 最终交期
                                if(StringUtils.isBlank(report.getCompareDateFlag()) || "1".equals(report.getCompareDateFlag()))
                                {
                                    if(DateUtils.getDaysMax(delivered.getDeliveryDate(),ck.getDeliveryDate()) >= 0)
                                    {
                                        ck.setDeliveryDateRateFlag(true);
                                        completedPayment++;
                                    }
                                }
                                // 首批交期
                                else if("2".equals(report.getCompareDateFlag()))
                                {
                                    if(DateUtils.getDaysMax(delivered.getFirstBatchDate(),ck.getFirstBatchDate()) >= 0)
                                    {
                                        ck.setDeliveryDateRateFlag(true);
                                        completedPayment++;
                                    }
                                }
                                break;
                            }
                        }
                    }
                    BigDecimal deliveryDateRate = new BigDecimal(completedPayment).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(orderList.size()),2,BigDecimal.ROUND_HALF_UP);
                    data.put("deliveryDateRate",deliveryDateRate); // 交货达成率
                }
            }
        }

        getNormalRiskList(data,orderList,report.getCompareDateFlag());

        // 获取部门的面积、金额、款数
        List<GroupOrgRelation> groupDeptList = report.getGroupDeptList();
        if(Collections3.isNotEmpty(groupDeptList))
        {
            for(GroupOrgRelation gr : groupDeptList)
            {
                Integer beforeCount = 0;
                BigDecimal beforeArea = BigDecimal.ZERO;
                BigDecimal beforeAmount = BigDecimal.ZERO;
                if(Collections3.isNotEmpty(beforeOrderList))
                {
                    for (Cockpit ck : beforeOrderList)
                    {
                        if(StringUtils.isNotBlank(ck.getGroupOrgId())  && ck.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            beforeCount++;
                            if(null != ck.getWorkingArea())
                            {
                                beforeArea = beforeArea.add(ck.getWorkingArea());
                            }
                            if(null != ck.getWorkingAmount())
                            {
                                beforeAmount = beforeAmount.add(ck.getWorkingAmount());
                            }
                        }
                    }
                }

                Integer count = 0;
                BigDecimal area = BigDecimal.ZERO;
                BigDecimal amount = BigDecimal.ZERO;
                BigDecimal costFee = BigDecimal.ZERO;
                BigDecimal netCostFee = BigDecimal.ZERO;
                Integer completedPayment = 0;
                if(Collections3.isNotEmpty(orderList))
                {
                    for(Cockpit ck : orderList)
                    {
                        if(StringUtils.isNotBlank(ck.getGroupOrgId())  && ck.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            count++;
                            if(null != ck.getWorkingArea())
                            {
                                area = area.add(ck.getWorkingArea());
                            }
                            if(null != ck.getWorkingAmount())
                            {
                                amount = amount.add(ck.getWorkingAmount());
                            }
                            if(null != ck.getCostFee())
                            {
                                costFee = costFee.add(ck.getCostFee());
                            }
                            if(null != ck.getNetCostFee())
                            {
                                netCostFee = netCostFee.add(ck.getNetCostFee());
                            }
                            if(null != ck.getDeliveryDateRateFlag() && ck.getDeliveryDateRateFlag())
                            {
                                completedPayment++;
                            }
                        }
                    }
                }
                gr.setCount(count); // 款数
                gr.setArea(setScale(area,2)); // 面积
                gr.setAmount(setScale(amount,2)); // 金额
                gr.setCostFee(setScale(costFee,2)); // 成本
                gr.setNetCostFee(setScale(netCostFee,2)); // 净成本

                // 获取上升/下降的款数、金额、面积
                BigDecimal countRate = getBigDecimalRate(new BigDecimal(count),new BigDecimal(beforeCount));
                BigDecimal areaRate = getBigDecimalRate(area,beforeArea);
                BigDecimal amountRate = getBigDecimalRate(amount,beforeAmount);
                Boolean countRateFlag = getBooleanFlag(new BigDecimal(count),new BigDecimal(beforeCount));
                Boolean areaRateFlag = getBooleanFlag(area,beforeArea);
                Boolean amountRateFlag = getBooleanFlag(amount,beforeAmount);
                gr.setCountRate(countRate); // 款数率
                gr.setAreaRate(areaRate); // 面积率
                gr.setAmountRate(amountRate); // 金额率
                gr.setCountRateFlag(countRateFlag); // 款数率上升/下降标志
                gr.setAreaRateFlag(areaRateFlag); // 面积率上升/下降标志
                gr.setAmountRateFlag(amountRateFlag); // 金额率上升/下降标志

                Integer dividend = count == 0 ? 1 : count;
                BigDecimal deliveryDateRate = new BigDecimal(completedPayment).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(dividend),2,BigDecimal.ROUND_HALF_UP);
                gr.setDeliveryDateRate(deliveryDateRate);
            }
        }
        if(Collections3.isNotEmpty(groupDeptList))
        {
            groupDeptList.sort((x, y) -> -((null == x.getAmount() ? BigDecimal.ZERO : x.getAmount())
                .compareTo(null == y.getAmount() ? BigDecimal.ZERO : y.getAmount())));
        }
        data.put("groupDeptList",groupDeptList);

        // 根据客户获取map集合
        Map<String,List<Cockpit>> map = new HashMap<>();
        for(Cockpit ck : orderList)
        {
            if(null == ck.getCustomer() || StringUtils.isBlank(ck.getCustomer().getRecordId()))
            {
                continue;
            }
            String key = ck.getCustomer().getRecordId();
            List<Cockpit> list = Lists.newArrayList();
            if(map.containsKey(key))
            {
                list = map.get(key);
            }
            list.add(ck);
            map.put(key,list);
        }
        // 获取客户数据
        List<Customer> customerList = Lists.newArrayList();
        if(null != map && map.size() > 0)
        {
            for (Map.Entry<String, List<Cockpit>> entry : map.entrySet())
            {
                Customer customer = new Customer();
                customer.setRecordId(entry.getKey());
                if (entry != null && entry.getValue() != null)
                {
                    List<Cockpit> list = entry.getValue();
                    if(StringUtils.isBlank(customer.getShortName()) && null != list.get(0).getCustomer())
                    {
                        customer.setShortName(list.get(0).getCustomer().getShortName());
                    }

                    Integer beforeCount = 0;
                    BigDecimal beforeArea = BigDecimal.ZERO;
                    BigDecimal beforeAmount = BigDecimal.ZERO;
                    if(Collections3.isNotEmpty(beforeOrderList))
                    {
                        for (Cockpit ck : beforeOrderList)
                        {
                            if(null != ck.getCustomer() && StringUtils.isNotBlank(ck.getCustomer().getRecordId())  && ck.getCustomer().getRecordId().equals(customer.getRecordId()))
                            {
                                beforeCount++;
                                if(null != ck.getWorkingArea())
                                {
                                    beforeArea = beforeArea.add(ck.getWorkingArea());
                                }
                                if(null != ck.getWorkingAmount())
                                {
                                    beforeAmount = beforeAmount.add(ck.getWorkingAmount());
                                }
                            }
                        }
                    }

                    Integer count = 0;
                    BigDecimal area = BigDecimal.ZERO;
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal costFee = BigDecimal.ZERO;
                    BigDecimal netCostFee = BigDecimal.ZERO;
                    Integer completedPayment = 0;
                    for(Cockpit ck : list)
                    {
                        count++;
                        if(null != ck.getWorkingArea())
                        {
                            area = area.add(ck.getWorkingArea());
                        }
                        if(null != ck.getWorkingAmount())
                        {
                            amount = amount.add(ck.getWorkingAmount());
                        }
                        if(null != ck.getCostFee())
                        {
                            costFee = costFee.add(ck.getCostFee());
                        }
                        if(null != ck.getNetCostFee())
                        {
                            netCostFee = netCostFee.add(ck.getNetCostFee());
                        }
                        if(null != ck.getDeliveryDateRateFlag() && ck.getDeliveryDateRateFlag())
                        {
                            completedPayment++;
                        }
                    }
                    customer.setCount(count); // 款数
                    customer.setArea(setScale(area,2)); // 面积
                    customer.setAmount(setScale(amount,2)); // 金额
                    customer.setCostFee(setScale(costFee,2)); // 成本
                    customer.setNetCostFee(setScale(netCostFee,2)); // 净成本

                    // 获取上升/下降的款数、金额、面积
                    BigDecimal countRate = getBigDecimalRate(new BigDecimal(count),new BigDecimal(beforeCount));
                    BigDecimal areaRate = getBigDecimalRate(area,beforeArea);
                    BigDecimal amountRate = getBigDecimalRate(amount,beforeAmount);
                    Boolean countRateFlag = getBooleanFlag(new BigDecimal(count),new BigDecimal(beforeCount));
                    Boolean areaRateFlag = getBooleanFlag(area,beforeArea);
                    Boolean amountRateFlag = getBooleanFlag(amount,beforeAmount);
                    customer.setCountRate(countRate); // 款数率
                    customer.setAreaRate(areaRate); // 面积率
                    customer.setAmountRate(amountRate); // 金额率
                    customer.setCountRateFlag(countRateFlag); // 款数率上升/下降标志
                    customer.setAreaRateFlag(areaRateFlag); // 面积率上升/下降标志
                    customer.setAmountRateFlag(amountRateFlag); // 金额率上升/下降标志

                    Integer dividend = count == 0 ? 1 : count;
                    BigDecimal deliveryDateRate = new BigDecimal(completedPayment).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(dividend),2,BigDecimal.ROUND_HALF_UP);
                    customer.setDeliveryDateRate(deliveryDateRate);
                }
                customerList.add(customer);
            }
        }
        if(Collections3.isNotEmpty(customerList))
        {
            customerList.sort((x, y) -> -((null == x.getAmount() ? BigDecimal.ZERO : x.getAmount())
                .compareTo(null == y.getAmount() ? BigDecimal.ZERO : y.getAmount())));
        }
        data.put("customerList",customerList);

        return data;
    }

    // 获取订单线数据
    public Map<String,Object>  getOrderLineData(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        report.setQueryType("1"); // 当月
        List<Cockpit> orderLineList = cockpitDao.getOrderLineData(report);

        report.setQueryType("2"); // 在线
        List<Cockpit> orderLineTwoList = cockpitDao.getOrderLineData(report);
        if(Collections3.isNotEmpty(orderLineTwoList))
        {
            // 获取正常、风险、延期集合
            getNormalRiskListTwo(data,orderLineTwoList,report.getCompareDateFlag());

            // 获取订单进度
            data.put("schedule",getOrderLineDataTwo(orderLineTwoList));
        }

        if(Collections3.isNotEmpty(orderLineList))
        {
            // 获取正常、风险、延期集合
            getNormalRiskList(data,orderLineList,report.getCompareDateFlag());

            // 获取部门的面积、金额、款数
            List<GroupOrgRelation> groupDeptList = report.getGroupDeptList();
            if(Collections3.isNotEmpty(groupDeptList))
            {
                for(GroupOrgRelation gr : groupDeptList)
                {
                    Integer count = 0;
                    BigDecimal area = BigDecimal.ZERO;
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal costFee = BigDecimal.ZERO;
                    BigDecimal netCostFee = BigDecimal.ZERO;
                    for(Cockpit ck : orderLineList)
                    {
                        if(StringUtils.isNotBlank(ck.getGroupOrgId())  && ck.getGroupOrgId().equals(gr.getGroupOrgId()))
                        {
                            count++;
                            if(null != ck.getWorkingArea())
                            {
                                area = area.add(ck.getWorkingArea());
                            }
                            if(null != ck.getWorkingAmount())
                            {
                                amount = amount.add(ck.getWorkingAmount());
                            }
                            if(null != ck.getCostFee())
                            {
                                costFee = costFee.add(ck.getCostFee());
                            }
                            if(null != ck.getNetCostFee())
                            {
                                netCostFee = netCostFee.add(ck.getNetCostFee());
                            }
                        }
                    }
                    gr.setCount(count);
                    gr.setArea(setScale(area,2));
                    gr.setAmount(setScale(amount,2));
                    gr.setCostFee(setScale(costFee,2));
                    gr.setNetCostFee(setScale(netCostFee,2));
                }
            }
            if(Collections3.isNotEmpty(groupDeptList))
            {
                groupDeptList.sort((x, y) -> -((null == x.getAmount() ? BigDecimal.ZERO : x.getAmount())
                    .compareTo(null == y.getAmount() ? BigDecimal.ZERO : y.getAmount())));
            }
            data.put("groupDeptList",groupDeptList);

            // 根据客户获取map集合
            Map<String,List<Cockpit>> map = new HashMap<>();
            for(Cockpit ck : orderLineList)
            {
                if(null == ck.getCustomer() || StringUtils.isBlank(ck.getCustomer().getRecordId()))
                {
                    continue;
                }
                String key = ck.getCustomer().getRecordId();
                List<Cockpit> list = Lists.newArrayList();
                if(map.containsKey(key))
                {
                    list = map.get(key);
                }
                list.add(ck);
                map.put(key,list);
            }
            // 获取客户数据
            List<Customer> customerList = Lists.newArrayList();
            if(null != map && map.size() > 0)
            {
                for (Map.Entry<String, List<Cockpit>> entry : map.entrySet())
                {
                    Customer customer = new Customer();
                    customer.setRecordId(entry.getKey());
                    if (entry != null && entry.getValue() != null)
                    {
                        List<Cockpit> list = entry.getValue();
                        if(StringUtils.isBlank(customer.getShortName()) && null != list.get(0).getCustomer())
                        {
                            customer.setShortName(list.get(0).getCustomer().getShortName());
                        }
                        Integer count = 0;
                        BigDecimal area = BigDecimal.ZERO;
                        BigDecimal amount = BigDecimal.ZERO;
                        BigDecimal costFee = BigDecimal.ZERO;
                        BigDecimal netCostFee = BigDecimal.ZERO;
                        for(Cockpit ck : list)
                        {
                            count++;
                            if(null != ck.getWorkingArea())
                            {
                                area = area.add(ck.getWorkingArea());
                            }
                            if(null != ck.getWorkingAmount())
                            {
                                amount = amount.add(ck.getWorkingAmount());
                            }
                            if(null != ck.getCostFee())
                            {
                                costFee = costFee.add(ck.getCostFee());
                            }
                            if(null != ck.getNetCostFee())
                            {
                                netCostFee = netCostFee.add(ck.getNetCostFee());
                            }
                        }
                        customer.setCount(count);
                        customer.setArea(setScale(area,2));
                        customer.setAmount(setScale(amount,2));
                        customer.setCostFee(setScale(costFee,2));
                        customer.setNetCostFee(setScale(netCostFee,2));
                    }
                    customerList.add(customer);
                }
            }
            if(Collections3.isNotEmpty(customerList))
            {
                // 获取客户列表的应收未收金额
                List<Customer> amountList = singleReceivableDao.getSingleReceivableAmount(customerList);
                // 获取历史亏损/盈利
                List<Customer> LossList = contractDao.getCusLossLimitData(customerList);
                for(Customer cus : customerList)
                {
                    if(Collections3.isNotEmpty(amountList))
                    {
                        for(Customer one : amountList)
                        {
                            if(one.getRecordId().equals(cus.getRecordId()))
                            {
                                cus.setReceivableAmount(one.getAmount());
                                break;
                            }
                        }
                    }
                    if(Collections3.isNotEmpty(LossList))
                    {
                        for(Customer two : LossList)
                        {
                            if(two.getRecordId().equals(cus.getRecordId()))
                            {
                                cus.setProfitLossAmount(two.getAmount());
                                break;
                            }
                        }
                    }
                }
                customerList.sort((x, y) -> -((null == x.getAmount() ? BigDecimal.ZERO : x.getAmount())
                    .compareTo(null == y.getAmount() ? BigDecimal.ZERO : y.getAmount())));
            }
            data.put("customerList",customerList);
        }
        return data;
    }

    // 获取正常、风险、延期集合
    public void getNormalRiskList(Map<String, Object> data,List<Cockpit> orderLineList,String compareDateFlag)
    {
        if(Collections3.isEmpty(orderLineList))
        {
            return;
        }
        Date nowDate = new Date();
        String dateStr = DateUtils.formatDate(nowDate);

        Integer sumCount = 0;
        BigDecimal sumArea = BigDecimal.ZERO;
        BigDecimal sumAmount = BigDecimal.ZERO;

        BigDecimal normalArea = BigDecimal.ZERO;
        BigDecimal postponeArea = BigDecimal.ZERO;
        BigDecimal riskArea = BigDecimal.ZERO;

        BigDecimal sumCostFee = BigDecimal.ZERO;
        BigDecimal sumNetCostFee = BigDecimal.ZERO;
        for(Cockpit orderLine : orderLineList)
        {
            Boolean postponeFlag = false; // 延期标志
            Boolean riskFlag = false; // 风险标志
            if(null == orderLine)
            {
                continue;
            }
            if(null == orderLine.getWorkingArea())
            {
                orderLine.setWorkingArea(BigDecimal.ZERO);
            }
            if(null == orderLine.getWorkingAmount())
            {
                orderLine.setWorkingAmount(BigDecimal.ZERO);
            }
            if(null == orderLine.getCostFee())
            {
                orderLine.setCostFee(BigDecimal.ZERO);
            }
            if(null == orderLine.getNetCostFee())
            {
                orderLine.setNetCostFee(BigDecimal.ZERO);
            }

            // 总的
            sumCount++;
            sumArea = sumArea.add(orderLine.getWorkingArea());
            sumAmount = sumAmount.add(orderLine.getWorkingAmount());
            sumCostFee = sumCostFee.add(orderLine.getCostFee());
            sumNetCostFee = sumNetCostFee.add(orderLine.getNetCostFee());
            if(StringUtils.isBlank(compareDateFlag) || "1".equals(compareDateFlag))
            {
                // 最终交期
                if(null != orderLine.getDeliveryDate())
                {
                    String deliveryDateStr = DateUtils.formatDate(orderLine.getDeliveryDate());
                    // 如果交货日期小于当前日期标志为延期
                    if(dateStr.compareTo(deliveryDateStr) > 0)
                    {
                        postponeFlag = true;
                    }
                    if(!postponeFlag)
                    {
                        // 未下料就判断<3天
                        Date changeDate = DateUtils.dateAdd(orderLine.getDeliveryDate(),-3);
                        String changeDateStr =  DateUtils.formatDate(changeDate);
                        if(dateStr.compareTo(changeDateStr) > 0)
                        {
                            riskFlag = true;
                        }
                    }
                }
            }
            else if("2".equals(compareDateFlag))
            {
                // 首批交期
                if(null != orderLine.getDeliveryDate())
                {
                    String deliveryDateStr = DateUtils.formatDate(orderLine.getFirstBatchDate());
                    // 如果交货日期小于当前日期标志为延期
                    if(dateStr.compareTo(deliveryDateStr) > 0)
                    {
                        postponeFlag = true;
                    }
                    if(!postponeFlag)
                    {
                        // 未下料就判断<3天
                        Date changeDate = DateUtils.dateAdd(orderLine.getFirstBatchDate(),-3);
                        String changeDateStr =  DateUtils.formatDate(changeDate);
                        if(dateStr.compareTo(changeDateStr) > 0)
                        {
                            riskFlag = true;
                        }
                    }
                }
            }
            if(postponeFlag)
            {
                postponeArea = postponeArea.add(orderLine.getWorkingArea());
            }
            else if(riskFlag)
            {
                riskArea = riskArea.add(orderLine.getWorkingArea());
            }
            else
            {
                normalArea = normalArea.add(orderLine.getWorkingArea());
            }
        }
        data.put("sumCount",sumCount); // 总款数
        data.put("sumArea",setScale(sumArea,2)); // 总面积
        data.put("sumAmount",setScale(sumAmount,2)); // 总金额
        data.put("postponeArea",setScale(postponeArea,2)); // 延期面积
        data.put("riskArea",setScale(riskArea,2)); // 风险面积
        data.put("normalArea",setScale(normalArea,2)); // 正常面积

        data.put("sumCostFee",setScale(sumCostFee,2)); // 总成本
        data.put("sumNetCostFee",setScale(sumNetCostFee,2)); // 总净成本
    }

    // 获取正常、风险、延期集合
    public void getNormalRiskListTwo(Map<String, Object> data,List<Cockpit> orderLineList,String compareDateFlag)
    {
        if(Collections3.isEmpty(orderLineList))
        {
            return;
        }
        Date nowDate = new Date();
        String dateStr = DateUtils.formatDate(nowDate);

        Integer sumCount = 0;
        BigDecimal sumArea = BigDecimal.ZERO;
        BigDecimal sumAmount = BigDecimal.ZERO;

        BigDecimal normalArea = BigDecimal.ZERO;
        BigDecimal postponeArea = BigDecimal.ZERO;
        BigDecimal riskArea = BigDecimal.ZERO;

        BigDecimal sumCostFee = BigDecimal.ZERO;
        BigDecimal sumNetCostFee = BigDecimal.ZERO;
        for(Cockpit orderLine : orderLineList)
        {
            Boolean postponeFlag = false; // 延期标志
            Boolean riskFlag = false; // 风险标志
            if(null == orderLine)
            {
                continue;
            }
            if(null == orderLine.getWorkingArea())
            {
                orderLine.setWorkingArea(BigDecimal.ZERO);
            }
            if(null == orderLine.getWorkingAmount())
            {
                orderLine.setWorkingAmount(BigDecimal.ZERO);
            }
            if(null == orderLine.getCostFee())
            {
                orderLine.setCostFee(BigDecimal.ZERO);
            }
            if(null == orderLine.getNetCostFee())
            {
                orderLine.setNetCostFee(BigDecimal.ZERO);
            }

            // 总的
            sumCount++;
            sumArea = sumArea.add(orderLine.getWorkingArea());
            sumAmount = sumAmount.add(orderLine.getWorkingAmount());
            sumCostFee = sumCostFee.add(orderLine.getCostFee());
            sumNetCostFee = sumNetCostFee.add(orderLine.getNetCostFee());
            if(StringUtils.isBlank(compareDateFlag) || "1".equals(compareDateFlag))
            {
                // 最终交期
                if(null != orderLine.getDeliveryDate())
                {
                    String deliveryDateStr = DateUtils.formatDate(orderLine.getDeliveryDate());
                    // 如果交货日期小于当前日期标志为延期
                    if(dateStr.compareTo(deliveryDateStr) > 0)
                    {
                        postponeFlag = true;
                    }
                    if(!postponeFlag)
                    {
                        // 未下料就判断<3天
                        Date changeDate = DateUtils.dateAdd(orderLine.getDeliveryDate(),-3);
                        String changeDateStr =  DateUtils.formatDate(changeDate);
                        if(dateStr.compareTo(changeDateStr) > 0)
                        {
                            riskFlag = true;
                        }
                    }
                }
            }
            else if("2".equals(compareDateFlag))
            {
                // 首批交期
                if(null != orderLine.getDeliveryDate())
                {
                    String deliveryDateStr = DateUtils.formatDate(orderLine.getFirstBatchDate());
                    // 如果交货日期小于当前日期标志为延期
                    if(dateStr.compareTo(deliveryDateStr) > 0)
                    {
                        postponeFlag = true;
                    }
                    if(!postponeFlag)
                    {
                        // 未下料就判断<3天
                        Date changeDate = DateUtils.dateAdd(orderLine.getFirstBatchDate(),-3);
                        String changeDateStr =  DateUtils.formatDate(changeDate);
                        if(dateStr.compareTo(changeDateStr) > 0)
                        {
                            riskFlag = true;
                        }
                    }
                }
            }
            if(postponeFlag)
            {
                postponeArea = postponeArea.add(orderLine.getWorkingArea());
            }
            else if(riskFlag)
            {
                riskArea = riskArea.add(orderLine.getWorkingArea());
            }
            else
            {
                normalArea = normalArea.add(orderLine.getWorkingArea());
            }
        }
        data.put("sumCountTwo",sumCount); // 总款数
        data.put("sumAreaTwo",setScale(sumArea,2)); // 总面积
        data.put("sumAmountTwo",setScale(sumAmount,2)); // 总金额
        data.put("postponeAreaTwo",setScale(postponeArea,2)); // 延期面积
        data.put("riskAreaTwo",setScale(riskArea,2)); // 风险面积
        data.put("normalAreaTwo",setScale(normalArea,2)); // 正常面积

        data.put("sumCostFeeTwo",setScale(sumCostFee,2)); // 总成本
        data.put("sumNetCostFeeTwo",setScale(sumNetCostFee,2)); // 总净成本
    }

    public Map<String,Object> returnDataMap(Map<String,Object> data,String workingName,BigDecimal workingArea)
    {
        if(null == workingArea)
        {
            workingArea = BigDecimal.ZERO;
        }
        Report report = new Report();
        if(data.containsKey(workingName))
        {
            report = (Report)data.get(workingName);
            report.setArea(report.getArea().add(workingArea));
        }
        else
        {
            report.setArea(workingArea);
        }
        data.put(workingName,report);
        return data;
    }

    public Map<String,Object> getOrderLineDataTwo(List<Cockpit> orderLineList)
    {
        Map<String,Object> data = new HashMap<>();
        Map<String,String> pbdMap = new HashMap<>();
        if(Collections3.isNotEmpty(orderLineList))
        {
            List<String> list = Lists.newArrayList();
            for(Cockpit cock : orderLineList)
            {
                if(StringUtils.isNotBlank(cock.getFactConDetailId()))
                {
                    list.add(cock.getFactConDetailId());
                }
            }
            if(Collections3.isEmpty(list))
            {
                return data;
            }
            // 获取未结束补料订单的合同明细
            List<String> oldDetailIdList = produceBatchDetailDao.getOldDetailList();
            if(Collections3.isNotEmpty(oldDetailIdList))
            {
                list.addAll(oldDetailIdList);
            }
            // 获取工程卡状态
            List<Notification> notiList = notificationDao.getDataByDetailId(list);
            if(Collections3.isNotEmpty(notiList))
            {
                for(Cockpit cock : orderLineList)
                {
                    for(Notification noti : notiList)
                    {
                        if(noti.getContractDetailId().equals(cock.getFactConDetailId()))
                        {
                            if(StringUtils.isNotBlank(noti.getCardStatus()))
                            {
                                cock.setCardStatus(noti.getCardStatus());
                            }
                            if(StringUtils.isNotBlank(noti.getStatus()))
                            {
                                cock.setNotiStatus(noti.getStatus());
                            }
                            cock.setCount(noti.getCount());
                            cock.setCloseTheCaseCause(noti.getCloseTheCaseCause());
                            cock.setFeedCause(noti.getFeedCause());
                            cock.setDeliveryArea(null == noti.getArea() ? "0" : noti.getArea().toString());
                            break;
                        }
                    }
                }
            }
            // 获取批次明细投料时间和状态
            List<ProduceBatchDetail> pbdList = produceBatchDetailDao.getDataBySaleDetailId(list);
            if(Collections3.isNotEmpty(pbdList))
            {
                for(Cockpit cock : orderLineList)
                {
                    cock.setPbdList(Lists.newArrayList());
                    for(ProduceBatchDetail pbd : pbdList)
                    {
                        if(pbd.getContractDetailId().equals(cock.getFactConDetailId()))
                        {
                            cock.getPbdList().add(pbd);
                        }
                    }
                }
            }
            for(Cockpit cock : orderLineList)
            {
                if(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString().equals(cock.getDetailStatus()))
                {
                    String workingName = "已录单";
                    data = returnDataMap(data,workingName,cock.getWorkingArea());
                    continue;
                }
                if(TypeKey.SL_CONTRACT_STATUS_WAIT.toString().equals(cock.getDetailStatus()))
                {
                    String workingName = "审批中";
                    data = returnDataMap(data,workingName,cock.getWorkingArea());
                    continue;
                }
                if(StringUtils.isBlank(cock.getGroupCenterId()))
                {
                    String workingName = "外协";
                    data = returnDataMap(data,workingName,cock.getWorkingArea());
                    continue;
                }
                if(StringUtils.isBlank(cock.getCardStatus()) || TypeKey.EG_PROCESSCARD_STATUS_DRAFT.toString().equals(cock.getCardStatus()))
                {
                    if(StringUtils.isBlank(cock.getCloseTheCaseCause()))
                    {
                        String workingName = "工程";
                        data = returnDataMap(data,workingName,cock.getWorkingArea());
                        continue;
                    }
                }
                // 只做资料不投产
                if(StringUtils.isBlank(cock.getDictOrderType()) && StringUtils.isNotBlank(cock.getDictMaterialType()))
                {
                    continue;
                }
                if(Collections3.isEmpty(cock.getPbdList()))
                {
                    // 不出尾数、工程完成、计划完成
                    if((null == cock.getCount() || cock.getCount().compareTo(0) == 0) && StringUtils.isBlank(cock.getCloseTheCaseCause()) && StringUtils.isBlank(cock.getFeedCause()))
                    {
                        String workingName = "计划";
                        data = returnDataMap(data,workingName,cock.getWorkingArea());
                        continue;
                    }
                }
                BigDecimal sentArea = BigDecimal.ZERO;
                if(Collections3.isNotEmpty(cock.getPbdList()))
                {
                    for(ProduceBatchDetail pbd : cock.getPbdList())
                    {
                        String key = pbd.getRecordId();
                        if(StringUtils.isNotBlank(pbd.getPbdcId()))
                        {
                            key = key + '-'+ pbd.getPbdcId();
                        }
                        if(pbdMap.containsKey(key))
                        {
                            continue;
                        }
                        pbdMap.put(key,key);
                        String acDistributeDate = null == pbd.getAcDistributeDate() ? null : DateUtils.formatDate(pbd.getAcDistributeDate());
                        String batchDetailStatus = null == pbd.getStatus() ? null : pbd.getStatus().toString();
                        String batchDetailType = pbd.getType();

                        if(StringUtils.isBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString())
                                && StringUtils.isNotBlank(batchDetailType) && batchDetailType.equals("1001"))
                        {
                            String workingName = "配料中";
                            data = returnDataMap(data,workingName,pbd.getArea());
                            continue;
                        }
                        if(StringUtils.isBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString())
                                && StringUtils.isNotBlank(batchDetailType) && batchDetailType.equals("1002"))
                        {
                            String workingName = "待投料";
                            data = returnDataMap(data,workingName,pbd.getArea());
                            continue;
                        }
                        if(StringUtils.isNotBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString()))
                        {
                            String workingName = "产线结存";
                            data = returnDataMap(data,workingName,pbd.getArea());
                            continue;
                        }
                        if(StringUtils.isNotBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString()))
                        {
                            sentArea = sentArea.add(pbd.getArea());
                            continue;
                        }
                    }
                }
                if(null != sentArea)
                {
                    if(StringUtils.isBlank(cock.getDeliveryArea()))
                    {
                        cock.setDeliveryArea("0");
                    }
                    if(sentArea.compareTo(new BigDecimal(cock.getDeliveryArea())) > 0)
                    {
                        String workingName = "待送货";
                        data = returnDataMap(data,workingName,sentArea.subtract(new BigDecimal(cock.getDeliveryArea())));
                    }
                }
            }
        }
        return data;
    }

    // 获取WIP数据
    public Map<String,Object> getWipData(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        // 获取当前区间的所有WIP数据
        List<CockpitSheet> wipList = cockpitSheetDao.getWipData(report);
        if(Collections3.isEmpty(wipList))
        {
           return data;
        }

        List<CockpitSheet> normalList = Lists.newArrayList();
        List<CockpitSheet> postponeList = Lists.newArrayList();
        List<CockpitSheet> riskList = Lists.newArrayList();
        Integer sumCount = 0;
        BigDecimal sumArea = BigDecimal.ZERO;
        Integer sumSampleCount = 0;
        BigDecimal sumSampleArea = BigDecimal.ZERO;
        Integer sumProductionCount = 0;
        BigDecimal sumProductionArea = BigDecimal.ZERO;

        BigDecimal normalArea = BigDecimal.ZERO;
        BigDecimal postponeArea = BigDecimal.ZERO;
        BigDecimal riskArea = BigDecimal.ZERO;

        // 获取批次明细最小交期列表
        List<BatchDetailCapacity> detailCapList = batchDetailCapacityDao.getMinDeliveryDateList(wipList);
        // 获取最大瓶颈工序日期列表
        List<BottleneckProcessUse> useList = bottleneckProcessUseDao.getMaxOccurrenceDateList(wipList);

        Date nowDate = new Date();
        String dateStr = DateUtils.formatDate(nowDate);

        // 获取正常、延期、风险数据
        Map<String,String> map = new HashMap<>();
        for(CockpitSheet wip : wipList)
        {
            Boolean postponeFlag = false; // 延期标志
            Boolean riskFlag = false; // 风险标志
            if(null == wip.getArea())
            {
                wip.setArea(BigDecimal.ZERO);
            }
            if(!map.containsKey(wip.getProduceBatchDetailId()))
            {
                // 总的
                sumCount ++;
                if(StringUtils.isNotBlank(wip.getOrderType()))
                {
                    // 样品
                    if("1".equals(wip.getOrderType()))
                    {
                        sumSampleCount++;
                        sumSampleArea = sumSampleArea.add(wip.getArea());
                    }
                    // 量产
                    else if("2".equals(wip.getOrderType()))
                    {
                        sumProductionCount++;
                        sumProductionArea = sumProductionArea.add(wip.getArea());
                    }
                }
            }
            map.put(wip.getProduceBatchDetailId(),wip.getProduceBatchDetailId());
            sumArea = sumArea.add(wip.getArea());

            Boolean flag = true;
            for(BatchDetailCapacity bc : detailCapList)
            {
                if(bc.getBatchDetailId().equals(wip.getProduceBatchDetailId()))
                {
                    flag = false;
                    // 如果交货日期小于当前日期标志为延期
                    if(null != bc.getDeliveryDateStr()){
                        if(dateStr.compareTo(bc.getDeliveryDateStr()) > 0)
                        {
                            postponeFlag = true;
                            break;
                        }
                    }
                }
            }
            if(flag)
            {
                // 如果交货日期小于当前日期标志为延期
                if(null != wip.getDeliveryDateStr()){
                    if(dateStr.compareTo(wip.getDeliveryDateStr()) > 0)
                    {
                        postponeFlag = true;
                    }
                }
            }

            if(!postponeFlag)
            {
               for(BottleneckProcessUse use : useList)
               {
                    if(use.getBatchDetailId().equals(wip.getProduceBatchDetailId()))
                    {
                        if(dateStr.compareTo(use.getOccurrenceDateStr()) > 0)
                        {
                            riskFlag = true;
                            break;
                        }
                    }
               }
            }
            if(postponeFlag)
            {
                postponeArea = postponeArea.add(wip.getArea());
                postponeList.add(wip);
            }
            else if(riskFlag)
            {
                riskArea = riskArea.add(wip.getArea());
                riskList.add(wip);
            }
            else
            {
                normalArea = normalArea.add(wip.getArea());
                normalList.add(wip);
            }
        }
        data.put("sumCount",sumCount);
        data.put("sumArea",setScale(sumArea,2));

        data.put("sumSampleCount",sumSampleCount);
        data.put("sumSampleArea",setScale(sumSampleArea,2));
        data.put("sumProductionCount",sumProductionCount);
        data.put("sumProductionArea",setScale(sumProductionArea,2));

        data.put("postponeArea",setScale(postponeArea,2));
        data.put("riskArea",setScale(riskArea,2));
        data.put("normalArea",setScale(normalArea,2));

        data.put("normalList",normalList);
        data.put("postponeList",postponeList);
        data.put("riskList",riskList);

        // 列表展示
        data.put("wipDetailList",getWipDetailList(report.getCompanyId(),wipList));

        // 获取原料采购数据
        List<PurchasingDetail> purDetailList = purchasingDetailDao.getListByCompanyId(report.getCompanyId());
        if(Collections3.isNotEmpty(purDetailList))
        {
            Date date = new Date();
            BigDecimal purSumArea = BigDecimal.ZERO;
            BigDecimal purNormalArea = BigDecimal.ZERO; // 正常
            BigDecimal purPostponeArea = BigDecimal.ZERO; // 延期
            BigDecimal purRiskArea = BigDecimal.ZERO; // 有风险
            for(PurchasingDetail pd : purDetailList)
            {
                if(null == pd.getArea())
                {
                    pd.setArea(BigDecimal.ZERO);
                }
                Integer day = DateUtils.getDaysMax(pd.getDeliveryDate(),date);
                if(null != day && day > 7)
                {
                    purPostponeArea = purPostponeArea.add(pd.getArea());
                }
                else if(null != day && day > 0)
                {
                    purRiskArea = purRiskArea.add(pd.getArea());
                }
                else
                {
                    purNormalArea = purNormalArea.add(pd.getArea());
                }
                purSumArea = purSumArea.add(pd.getArea());
            }
            data.put("purSumArea",setScale(purSumArea,4));
            data.put("purNormalArea",setScale(purNormalArea,4));
            data.put("purPostponeArea",setScale(purPostponeArea,4));
            data.put("purRiskArea",setScale(purRiskArea,4));
        }
        return data;
    }

    public BigDecimal setScale(BigDecimal value,Integer digit)
    {
        if(null == value || value.compareTo(BigDecimal.ZERO) <= 0)
        {
            return BigDecimal.ZERO;
        }
        if(null == digit)
        {
            digit = 0;
        }
        return value.setScale(digit, BigDecimal.ROUND_HALF_UP);
    }

    public List<BottleneckProcess> getWipDetailList(String companyId,List<CockpitSheet> wipList)
    {
        if(StringUtils.isBlank(companyId))
        {
            companyId = "17";
        }
        Company company = new Company(companyId);

        // 查询瓶颈工序列表
        BottleneckProcess query = new BottleneckProcess();
        query.setCompany(company);
        List<BottleneckProcess> processList = bottleneckProcessDao.getProcessList(query);
        if(Collections3.isEmpty(processList))
        {
            processList = Lists.newArrayList();
        }
        // 第一个仓库发料
        BottleneckProcess one = new BottleneckProcess();
        one.setProcessName("仓库发料");
        processList.add(0,one);

        // 初始时间处理
        Date nowDate = new Date();

        // 获取当日安排的任务
        BottleneckProcessUse useQuery = new BottleneckProcessUse();
        useQuery.setCompany(company);
        useQuery.setOccurrenceDate(nowDate);
        List<BottleneckProcessUse> useList = planSchedulingDao.processDetailsThreeA(useQuery);

        // 获取当日累计完成的瓶颈工序列表
        useQuery.setDateType(1);
        useQuery.setQueryDate(DateUtils.formatDate(useQuery.getOccurrenceDate()));
        List<BottleneckProcessUse> tatalUseList = bottleneckProcessUseDao.getArrangeCompleteListThree(useQuery);

        Map<String,String> map = new HashMap<>();
        for(BottleneckProcess bp : processList)
        {
            Integer balanceCount = 0; // 结存款数
            BigDecimal balanceArea = BigDecimal.ZERO; // 结存面积
            for(CockpitSheet wip : wipList)
            {
                if(StringUtils.isBlank(bp.getRecordId()))
                {
                    // 仓库发料
                    if(null != wip.getSeqNum() && wip.getSeqNum().compareTo(0) == 1 && StringUtils.isBlank(wip.getProduceRecordId()))
                    {
                        if(!map.containsKey(wip.getProduceBatchDetailId()))
                        {
                            balanceCount++;
                        }
                        if(null != wip.getArea())
                        {
                            balanceArea = balanceArea.add(wip.getArea());
                        }
                    }
                }
                else
                {
                    // 瓶颈工序一致(设置了曝光/丝印的也要保持一致)
                    if(StringUtils.isNotBlank(wip.getProcessManagementId()) && bp.getProcessManagementId().equals(wip.getProcessManagementId())
                        && (StringUtils.isBlank(bp.getProductionType()) || bp.getProductionType().equals(wip.getProcessValueId())))
                    {
                        if(!map.containsKey(wip.getProduceBatchDetailId()))
                        {
                            balanceCount++;
                        }
                        if(null != wip.getArea())
                        {
                            balanceArea = balanceArea.add(wip.getArea());
                        }
                    }
                }
                map.put(wip.getProduceBatchDetailId(),wip.getProduceBatchDetailId());
            }
            Integer arrangeCount = 0; // 安排款数
            BigDecimal arrangeArea = BigDecimal.ZERO; // 安排面积
            Integer completeCount = 0; // 完成款数
            Integer taskCount = 0; // 任务款数
            Integer otherCount = 0; // 其它款数
            BigDecimal completeArea = BigDecimal.ZERO; // 完成面积
            BigDecimal taskArea = BigDecimal.ZERO; // 任务面积
            BigDecimal otherArea = BigDecimal.ZERO; // 其它面积
            if(StringUtils.isNotBlank(bp.getRecordId()))
            {
                if(Collections3.isNotEmpty(useList))
                {
                    for(BottleneckProcessUse use : useList)
                    {
                        if(null == use.getUseArea() || use.getUseArea().compareTo(BigDecimal.ZERO) <= 0)
                        {
                            continue;
                        }
                        if(bp.getProcessManagementId().equals(use.getProcessManagementId()) && (StringUtils.isBlank(bp.getProductionType()) || bp.getProductionType().equals(use.getProductionType())))
                        {
                            arrangeCount++;
                            arrangeArea = arrangeArea.add(use.getUseArea());
                        }
                    }
                }
                if(Collections3.isNotEmpty(tatalUseList))
                {
                    for(BottleneckProcessUse use : tatalUseList)
                    {
                        if(null == use.getUseArea() || use.getUseArea().compareTo(BigDecimal.ZERO) <= 0)
                        {
                            continue;
                        }
                        if(bp.getProcessManagementId().equals(use.getProcessManagementId()) && (StringUtils.isBlank(bp.getProductionType()) || bp.getProductionType().equals(use.getProductionType())))
                        {
                            completeCount++;
                            completeArea = completeArea.add(use.getUseArea());
                            if(DateUtils.formatDate(use.getOccurrenceDate()).equals(DateUtils.formatDate(nowDate)))
                            {
                                taskCount++;
                                taskArea = taskArea.add(use.getUseArea());
                            }
                            else
                            {
                                otherCount++;
                                otherArea = otherArea.add(use.getUseArea());
                            }
                        }
                    }
                }
            }
            bp.setBalanceCount(balanceCount);
            bp.setBalanceArea(setScale(balanceArea,2));
            bp.setArrangeCount(arrangeCount);
            bp.setArrangeArea(setScale(arrangeArea,2));
            bp.setCompleteCount(completeCount);
            bp.setTaskCount(taskCount);
            bp.setOtherCount(otherCount);
            bp.setCompleteArea(setScale(completeArea,2));
            bp.setTaskArea(setScale(taskArea,2));
            bp.setOtherArea(setScale(otherArea,2));
        }
        return processList;
    }

    public Map<String, Object> getReportTotal(Report report){
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> dataTwo = new HashMap<>();
        if(report == null){
            data.put("result", "fail");
            data.put("message", "参数传递失败，请刷新重试!");
            return data;
        }
        ChangeDataUtils utils = new ChangeDataUtils();
        String result = null;
        if(!"3".equals(report.getDimensionType()))
        {
            result = utils.getReportAuthority(report);
            if (result.equals("fail")){
                data.put("result","fail");
                data.put("message","你还没有该权限");
                return data;
            }
        }
        String terminal = "1";
        if(StringUtils.isNotBlank(report.getName())) {
            String moduleType = null;
            switch (report.getName()) {
                case "接单":
                    moduleType = "1";
                    break;
                case "出货":
                    moduleType = "2";
                    break;
                case "回款":
                    moduleType = "3";
                    break;
                case "客诉":
                    moduleType = "6";
                    break;
                case "品质":
                    moduleType = "7";
                    terminal = null;
                    break;
                case "采购":
                    moduleType = "4,5";
                    break;
            }
            report.setModuleType(moduleType);
            report.setGroupOrgId(result);
            report.setTerminal(terminal);

            Date nowDate = new Date();
            report.setQueryDate(DateUtils.formatDate(nowDate));


            Integer size = 3;
            if(null != report.getAnalyticalNum())
            {
                size = report.getAnalyticalNum();
            }
            String queryDateStrs = null;// 时间组
            List<String> dateStrList = Lists.newArrayList();
            if (report.getDateType().compareTo(4) != 0) {
                for (int i = 0; i < size; i++) {
                    Date date = DateUtils.parseDate(report.getQueryDate());
                    String dateStr = null;
                    switch (report.getDateType()) {
                        // 日
                        case 1:
                            dateStr = DateUtils.getDateBefore(date, i, 1);
                            dateStrList.add(dateStr);
                            if (StringUtils.isNotBlank(queryDateStrs)) {
                                queryDateStrs = queryDateStrs + ",'" + dateStr + "'";
                            } else {
                                queryDateStrs = "'" + dateStr + "'";
                            }
                            break;
                        // 周
                        case 2:
                            dateStr = DateUtils.getDateBefore(date, i  * 7, 2);
                            if (StringUtils.isNotBlank(queryDateStrs)) {
                                queryDateStrs = queryDateStrs + "," + dateStr;
                            } else {
                                queryDateStrs = dateStr;
                            }

                            dateStrList.add(dateStr);
                            break;
                        // 月
                        case 3:
                            dateStr = DateUtils.getDateBefore(date, i, 3);
                            dateStrList.add(dateStr);
                            if (StringUtils.isNotBlank(queryDateStrs)) {
                                queryDateStrs = queryDateStrs + "," + dateStr;
                            } else {
                                queryDateStrs = dateStr;
                            }
                            break;
                    }
                }
                report.setQueryDateStrs(queryDateStrs);
            }

            String nowStr = null;
            String yesterStr = null;
            switch (report.getDateType()) {
                // 日
                case 1:
                    nowStr = DateUtils.formatDate(nowDate);
                    yesterStr =  DateUtils.getDateBefore(nowDate, 1, 1);
                    break;
                // 周
                case 2:
                    nowStr = DateUtils.getDateBefore(nowDate, 0, 2);
                    yesterStr =  DateUtils.getDateBefore(nowDate, 7, 2);
                    break;
                // 月
                case 3:
                    nowStr =DateUtils.getDateBefore(nowDate, 0, 3);
                    yesterStr =  DateUtils.getDateBefore(nowDate, 1, 3);
                    break;
                // 时间区间
                case 4:
                    if(StringUtils.isNotBlank(report.getSentTimeEndQr()))
                    {
                        nowStr = report.getSentTimeEndQr();
                        yesterStr =  DateUtils.getDateBefore(DateUtils.parseDate(nowStr), 1, 1);
                    }
                    break;
            }
            data.put("nowStr",nowStr);
            data.put("yesterStr",yesterStr);

            List<CockpitSheet> dateList = Lists.newArrayList();
            switch (report.getDimensionType()) {
                // 客户
                case "1":
                    dateList = cockpitSheetDao.getCustomListTotalDetail(report);
                    break;
                // 部门
                case "2":
                    dateList = cockpitSheetDao.getDepartListTotalDetail(report);
                    break;
                // 供应商
                case "3":
                    dateList = cockpitSheetDao.getSupplierListTotalDetail(report);
                    break;
                // 业务员
                case "4":
                    dateList = cockpitSheetDao.getSalesListTotalDetail(report);
                    break;
            }
            if(StringUtils.isNotBlank(report.getSentTimeStartQr()) && StringUtils.isNotBlank(report.getSentTimeEndQr()))
            {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(DateUtils.parseDate(report.getSentTimeStartQr()));
                dateStrList = Lists.newArrayList();
                while (calendar.getTime().compareTo(DateUtils.parseDate(report.getSentTimeEndQr())) <= 0)
                {
                    String date = DateUtils.formatDate(calendar.getTime());
                    dateStrList.add(date);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                }
            }

            for (String datetwo : dateStrList)
            {
                List<CockpitSheet> sheetListTwo = Lists.newArrayList();
                if (Collections3.isNotEmpty(dateList)) {
                    for (CockpitSheet sheetTwo : dateList) {
                        if (sheetTwo.getDate().compareTo(datetwo) == 0) {
                            sheetListTwo.add(sheetTwo);
                        }
                    }
                }
                if (Collections3.isEmpty(sheetListTwo)) {
                    CockpitSheet objtwo = new CockpitSheet();
                    objtwo.setDate(datetwo);
                    objtwo.setArea(new BigDecimal(0));
                    objtwo.setPcsQty(0);
                    objtwo.setMoney(new BigDecimal(0));
                    objtwo.setAmount("0");
                    objtwo.setDepartName("无");
                    sheetListTwo.add(objtwo);
                }
                dataTwo.put(datetwo, sheetListTwo);
            }

        }
        data.put("dataTwo",dataTwo);
        return data;
    }
    //获取六张报表明细
    public Map<String,Object>getOrderDetail(Report report){
        Map<String,Object> data = new HashMap<>();
        if(report == null){
            data.put("result", "fail");
            data.put("message", "参数传递失败，请刷新重试!");
            return data;
        }
        ChangeDataUtils utils = new ChangeDataUtils();
        String result = null;
        if(!"3".equals(report.getDimensionType()))
        {
            result = utils.getReportAuthority(report);
            if (result.equals("fail")){
                data.put("result","fail");
                data.put("message","你还没有该权限");
                return data;
            }
        }
        report.setGroupOrgId(result);

        Date nowDate = new Date();
        report.setQueryDate(DateUtils.formatDate(nowDate));

        if (report.getDateType().compareTo(4) != 0) {
            Integer size = report.getAnalyticalNum();
            String queryDateStrs = null;// 时间组
            for (int i = 0; i < size; i++) {
                Date date = DateUtils.parseDate(report.getQueryDate());
                String dateStr = null;
                switch (report.getDateType()) {
                    // 日
                    case 1:
                        dateStr = DateUtils.getDateBefore(date, i, 1);
                        if (StringUtils.isNotBlank(queryDateStrs)) {
                            queryDateStrs = queryDateStrs + ",'" + dateStr + "'";
                        } else {
                            queryDateStrs = "'" + dateStr + "'";
                        }
                        break;
                    // 周
                    case 2:
                        dateStr = DateUtils.getDateBefore(date, i * 7, 2);
                        if (StringUtils.isNotBlank(queryDateStrs)) {
                            queryDateStrs = queryDateStrs + "," + dateStr;
                        } else {
                            queryDateStrs = dateStr;
                        }
                        break;
                    // 月
                    case 3:
                        dateStr = DateUtils.getDateBefore(date, i, 3);
                        if (StringUtils.isNotBlank(queryDateStrs)) {
                            queryDateStrs = queryDateStrs + "," + dateStr;
                        } else {
                            queryDateStrs = dateStr;
                        }
                        break;
                }
            }
            report.setQueryDateStrs(queryDateStrs);
        }
        switch (report.getName()) {
            case "接单":
                List<ContractDetail> contractDetail = contractDetailDao.getReportContractDetail(report);
                data.put("allReportDetail",contractDetail);
                break;
            case "出货":
                List<DeliveryDetail> deliveryDetail = deliveryDetailDao.getReportDetail(report);
                data.put("allReportDetail",deliveryDetail);
                break;
            case "回款":
                List<SingleReceivableDetail> singleRecive = singleReceivableDetailDao.getReportSingleRece(report);
                data.put("allReportDetail",singleRecive);
                break;
            case "客诉":
                List<RejectApplication> rejectDetail = rejectApplicationDao.getReportReject(report);
                data.put("allReportDetail",rejectDetail);
                break;
            case "品质":
                List<Inspect> inspect = inspectDao.getReportInspect(report);
                data.put("allReportDetail",inspect);
                break;
            case "采购":
                List<PurchasingDetail>list = new ArrayList<>();
                List<PurchasingDetail> purchDetail = purchasingDetailDao.getReportPurchasingDetail(report);
//                List<PrdorderDetail> prdorderDetail =prdorderDetailDao.getReportPrdorderDetail(report);
                for(PurchasingDetail purch : purchDetail){
                    PurchasingDetail purchasingDetail = new PurchasingDetail();
                    purchasingDetail.setArea(purch.getArea());
                    purchasingDetail.setAmount(purch.getAmount());
                    purchasingDetail.setQuantity(purch.getQuantity());
                    purchasingDetail.setCustomerPo(purch.getCustomerPo());
                    purchasingDetail.setCustomerModel(purch.getCustomerModel());
                    purchasingDetail.setCraftNo(purch.getCraftNo());
                    list.add(purchasingDetail);
                }
//                for(PrdorderDetail prdo:prdorderDetail){
//                    PurchasingDetail purchasingDetail = new PurchasingDetail();
//                    purchasingDetail.setArea(prdo.getArea());
//                    purchasingDetail.setAmount(prdo.getAmount());
//                    purchasingDetail.setQuantity(BigDecimal.valueOf(prdo.getQuantity()));
//                    purchasingDetail.setCustomerPo(prdo.getCustomerPo());
//                    purchasingDetail.setCustomerModel(prdo.getCustomerModel());
//                    list.add(purchasingDetail);
//                }
                data.put("allReportDetail",list);
                break;
        }
    return data;
    }

    public Map<String,Object> getOrderLineWipData(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        if(report == null || StringUtils.isBlank(report.getReportType())){
            data.put("result", "fail");
            data.put("message", "参数传递失败，请刷新重试!");
            return data;
        }
        switch (report.getReportType())
        {
            // 订单
            case "1":
                if(StringUtils.isNotBlank(report.getChangeType()) && ("订单正常".equals(report.getChangeType()) || "订单风险".equals(report.getChangeType())
                    || "订单延期".equals(report.getChangeType()) || "已录单".equals(report.getChangeType())
                    || "审批中".equals(report.getChangeType()) || "外协".equals(report.getChangeType())
                    || "工程".equals(report.getChangeType()) || "计划".equals(report.getChangeType())
                    || "配料中".equals(report.getChangeType()) || "待投料".equals(report.getChangeType())
                    || "产线结存".equals(report.getChangeType()) || "待送货".equals(report.getChangeType())))
                {
                    report.setQueryType("2");
                }
                else
                {
                    report.setQueryType("1");
                }
                // 获取订单数据
                List<Cockpit> orderLineList = cockpitDao.getOrderLineData(report);
                if(StringUtils.isNotBlank(report.getChangeType()))
                {
                    List<String> list = Lists.newArrayList();
                    for(Cockpit cock : orderLineList)
                    {
                        if(StringUtils.isNotBlank(cock.getFactConDetailId()))
                        {
                            list.add(cock.getFactConDetailId());
                        }
                    }
                    // 获取未结束补料订单的合同明细
                    List<String> oldDetailIdList = produceBatchDetailDao.getOldDetailList();
                    if(Collections3.isNotEmpty(oldDetailIdList))
                    {
                        list.addAll(oldDetailIdList);
                    }
                    // 获取工程卡状态
                    List<Notification> notiList = notificationDao.getDataByDetailId(list);
                    if(Collections3.isNotEmpty(notiList))
                    {
                        for(Cockpit cock : orderLineList)
                        {
                            for(Notification noti : notiList)
                            {
                                if(noti.getContractDetailId().equals(cock.getFactConDetailId()))
                                {
                                    if(StringUtils.isNotBlank(noti.getCardStatus()))
                                    {
                                        cock.setCardStatus(noti.getCardStatus());
                                    }
                                    if(StringUtils.isNotBlank(noti.getStatus()))
                                    {
                                        cock.setNotiStatus(noti.getStatus());
                                    }
                                    cock.setCount(noti.getCount());
                                    cock.setCloseTheCaseCause(noti.getCloseTheCaseCause());
                                    cock.setFeedCause(noti.getFeedCause());
                                    cock.setDeliveryArea(null == noti.getArea() ? "0" : noti.getArea().toString());
                                    break;
                                }
                            }
                        }
                    }
                    // 获取批次明细投料时间和状态
                    List<ProduceBatchDetail> pbdList = produceBatchDetailDao.getDataBySaleDetailId(list);
                    if(Collections3.isNotEmpty(pbdList))
                    {
                        for(Cockpit cock : orderLineList)
                        {
                            cock.setPbdList(Lists.newArrayList());
                            for(ProduceBatchDetail pbd : pbdList)
                            {
                                if(pbd.getContractDetailId().equals(cock.getFactConDetailId()))
                                {
                                    cock.getPbdList().add(pbd);
                                }
                            }
                        }
                    }
                    List<Cockpit> dataList = Lists.newArrayList();
                    if(Collections3.isNotEmpty(orderLineList))
                    {
                        Date nowDate = new Date();
                        String dateStr = DateUtils.formatDate(nowDate);
                        for(Cockpit detail : orderLineList)
                        {
                            if("订单正常".equals(report.getChangeType()) || "订单风险".equals(report.getChangeType())
                            || "订单延期".equals(report.getChangeType()))
                            {
                                Boolean postponeFlag = false; // 延期标志
                                Boolean riskFlag = false; // 风险标志
                                if(StringUtils.isBlank(report.getCompareDateFlag()) || "1".equals((report.getCompareDateFlag())))
                                {
                                    // 最终交期
                                    if(null != detail.getDeliveryDate())
                                    {
                                        String deliveryDateStr = DateUtils.formatDate(detail.getDeliveryDate());
                                        // 如果交货日期小于当前日期标志为延期
                                        if(dateStr.compareTo(deliveryDateStr) > 0)
                                        {
                                            postponeFlag = true;
                                        }
                                        if(!postponeFlag)
                                        {
                                            // 未下料就判断<3天
                                            Date changeDate = DateUtils.dateAdd(detail.getDeliveryDate(),-3);
                                            String changeDateStr =  DateUtils.formatDate(changeDate);
                                            if(dateStr.compareTo(changeDateStr) > 0)
                                            {
                                                riskFlag = true;
                                            }
                                        }
                                    }
                                }
                                else if("2".equals((report.getCompareDateFlag())))
                                {
                                    // 首批交期
                                    if(null != detail.getDeliveryDate())
                                    {
                                        String deliveryDateStr = DateUtils.formatDate(detail.getFirstBatchDate());
                                        // 如果交货日期小于当前日期标志为延期
                                        if(dateStr.compareTo(deliveryDateStr) > 0)
                                        {
                                            postponeFlag = true;
                                        }
                                        if(!postponeFlag)
                                        {
                                            // 未下料就判断<3天
                                            Date changeDate = DateUtils.dateAdd(detail.getFirstBatchDate(),-3);
                                            String changeDateStr =  DateUtils.formatDate(changeDate);
                                            if(dateStr.compareTo(changeDateStr) > 0)
                                            {
                                                riskFlag = true;
                                            }
                                        }
                                    }
                                }
                                switch (report.getChangeType())
                                {
                                    case "订单正常":
                                        if(!(postponeFlag || riskFlag))
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                    case "订单风险":
                                        if(riskFlag)
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                    case "订单延期":
                                        if(postponeFlag)
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                }
                            }
                            else
                            {
                                switch (report.getChangeType())
                                {
                                    case "已录单":
                                        if(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString().equals(detail.getDetailStatus()))
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                    case "审批中":
                                        if(TypeKey.SL_CONTRACT_STATUS_WAIT.toString().equals(detail.getDetailStatus()))
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                    case "外协":
                                        if(StringUtils.isBlank(detail.getGroupCenterId()))
                                        {
                                            dataList.add(detail);
                                        }
                                        break;
                                    case "工程":
                                        if(StringUtils.isBlank(detail.getCardStatus()) || TypeKey.EG_PROCESSCARD_STATUS_DRAFT.toString().equals(detail.getCardStatus()))
                                        {
                                            if(StringUtils.isBlank(detail.getCloseTheCaseCause()))
                                            {
                                                dataList.add(detail);
                                            }
                                        }
                                        break;
                                    case "计划":
                                        // 只做资料不投产
                                        if(StringUtils.isBlank(detail.getDictOrderType()) && StringUtils.isNotBlank(detail.getDictMaterialType()))
                                        {
                                            continue;
                                        }
                                        if(Collections3.isEmpty(detail.getPbdList()))
                                        {
                                            // 不出尾数、工程完成、计划完成
                                            if((null == detail.getCount() || detail.getCount().compareTo(0) == 0) && StringUtils.isBlank(detail.getCloseTheCaseCause()) && StringUtils.isBlank(detail.getFeedCause()))
                                            {
                                                dataList.add(detail);
                                            }
                                        }
                                        break;
                                    case "配料中":
                                        if(Collections3.isNotEmpty(detail.getPbdList()))
                                        {
                                            for(ProduceBatchDetail pbd : detail.getPbdList())
                                            {
                                                String acDistributeDate = null == pbd.getAcDistributeDate() ? null : DateUtils.formatDate(pbd.getAcDistributeDate());
                                                String batchDetailStatus = null == pbd.getStatus() ? null : pbd.getStatus().toString();
                                                String batchDetailType = pbd.getType();
                                                if(StringUtils.isBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString())
                                                        && StringUtils.isNotBlank(batchDetailType) && batchDetailType.equals("1001"))
                                                {
                                                    dataList.add(detail);
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                    case "待投料":
                                        if(Collections3.isNotEmpty(detail.getPbdList()))
                                        {
                                            for(ProduceBatchDetail pbd : detail.getPbdList())
                                            {
                                                String acDistributeDate = null == pbd.getAcDistributeDate() ? null : DateUtils.formatDate(pbd.getAcDistributeDate());
                                                String batchDetailStatus = null == pbd.getStatus() ? null : pbd.getStatus().toString();
                                                String batchDetailType = pbd.getType();
                                                if(StringUtils.isBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString())
                                                        && StringUtils.isNotBlank(batchDetailType) && batchDetailType.equals("1002"))
                                                {
                                                    dataList.add(detail);
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                    case "产线结存":
                                        if(Collections3.isNotEmpty(detail.getPbdList()))
                                        {
                                            for(ProduceBatchDetail pbd : detail.getPbdList())
                                            {
                                                String acDistributeDate = null == pbd.getAcDistributeDate() ? null : DateUtils.formatDate(pbd.getAcDistributeDate());
                                                String batchDetailStatus = null == pbd.getStatus() ? null : pbd.getStatus().toString();
                                                if(StringUtils.isNotBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString()))
                                                {
                                                    dataList.add(detail);
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                    case "待送货":
                                        if(Collections3.isNotEmpty(detail.getPbdList()))
                                        {
                                            for(ProduceBatchDetail pbd : detail.getPbdList())
                                            {
                                                String acDistributeDate = null == pbd.getAcDistributeDate() ? null : DateUtils.formatDate(pbd.getAcDistributeDate());
                                                String batchDetailStatus = null == pbd.getStatus() ? null : pbd.getStatus().toString();
                                                if(StringUtils.isNotBlank(acDistributeDate) && StringUtils.isNotBlank(batchDetailStatus) && batchDetailStatus.equals(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString()))
                                                {
                                                    dataList.add(detail);
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                }
                            }
                        }
                    }
                    data.put("detailList",dataList);
                }
                else
                {
                    data.put("detailList",orderLineList);
                }
                break;
            // 生产
            case "2":
                String factId = CompanyUtil.getInstance().getFactId();
                report.setFactId(factId);
                List<CockpitSheet> wipList = cockpitSheetDao.getWipData(report);
                if(StringUtils.isNotBlank(report.getChangeType()))
                {
                    if("生产正常".equals(report.getChangeType()) || "生产风险".equals(report.getChangeType())
                    || "生产延期".equals(report.getChangeType()))
                    {
                        if(Collections3.isEmpty(wipList))
                        {
                            return data;
                        }
                        List<CockpitSheet> dataList = Lists.newArrayList();
                        // 获取批次明细最小交期列表
                        List<BatchDetailCapacity> detailCapList = batchDetailCapacityDao.getMinDeliveryDateList(wipList);
                        // 获取最大瓶颈工序日期列表
                        List<BottleneckProcessUse> useList = bottleneckProcessUseDao.getMaxOccurrenceDateList(wipList);
                        Date nowDate = new Date();
                        String dateStr = DateUtils.formatDate(nowDate);

                        // 获取正常、延期、风险数据
                        for(CockpitSheet wip : wipList)
                        {
                            Boolean postponeFlag = false; // 延期标志
                            Boolean riskFlag = false; // 风险标志
                            if(null == wip.getArea())
                            {
                                wip.setArea(BigDecimal.ZERO);
                            }
                            for(BatchDetailCapacity bc : detailCapList)
                            {
                                if(bc.getBatchDetailId().equals(wip.getProduceBatchDetailId()))
                                {
                                    // 如果交货日期小于当前日期标志为延期
                                    if(null != bc.getDeliveryDateStr()){
                                        if(dateStr.compareTo(bc.getDeliveryDateStr()) > 0)
                                        {
                                            postponeFlag = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            if(!postponeFlag)
                            {
                                for(BottleneckProcessUse use : useList)
                                {
                                    if(use.getBatchDetailId().equals(wip.getProduceBatchDetailId()))
                                    {
                                        if(dateStr.compareTo(use.getOccurrenceDateStr()) > 0)
                                        {
                                            riskFlag = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            switch (report.getChangeType())
                            {
                                case "生产正常":
                                    if(!(postponeFlag || riskFlag))
                                    {
                                        dataList.add(wip);
                                    }
                                    break;
                                case "生产风险":
                                    if(riskFlag)
                                    {
                                        dataList.add(wip);
                                    }
                                    break;
                                case "生产延期":
                                    if(postponeFlag)
                                    {
                                        dataList.add(wip);
                                    }
                                    break;
                            }
                        }
                        data.put("type",1);
                        data.put("detailList",dataList);
                    }
                    else if("采购正常".equals(report.getChangeType()) || "采购风险".equals(report.getChangeType())
                        || "采购延期".equals(report.getChangeType()))
                    {
                        // 获取原料采购数据
                        List<PurchasingDetail> dataList = Lists.newArrayList();
                        List<PurchasingDetail> purDetailList = purchasingDetailDao.getListByCompanyId(factId);
                        if(Collections3.isNotEmpty(purDetailList))
                        {
                            Date date = new Date();
                            for(PurchasingDetail pd : purDetailList)
                            {
                                Boolean postponeFlag = false; // 延期标志
                                Boolean riskFlag = false; // 风险标志
                                if(null == pd.getArea())
                                {
                                    pd.setArea(BigDecimal.ZERO);
                                }
                                Integer day = DateUtils.getDaysMax(pd.getDeliveryDate(),date);
                                if(null != day && day > 7)
                                {
                                    postponeFlag = true;
                                }
                                else if(null != day && day > 0)
                                {
                                    riskFlag = true;
                                }
                                switch (report.getChangeType())
                                {
                                    case "采购正常":
                                        if(!(postponeFlag || riskFlag))
                                        {
                                            dataList.add(pd);
                                        }
                                        break;
                                    case "采购风险":
                                        if(riskFlag)
                                        {
                                            dataList.add(pd);
                                        }
                                        break;
                                    case "采购延期":
                                        if(postponeFlag)
                                        {
                                            dataList.add(pd);
                                        }
                                        break;
                                }
                            }
                        }
                        data.put("type",2);
                        data.put("detailList",dataList);
                    }
                }
                else
                {
                    if(StringUtils.isNotBlank(report.getProcessManagementId()))
                    {
                        if("无".equals(report.getProcessManagementId()))
                        {
                            List<CockpitSheet> dataList = Lists.newArrayList();
                            for(CockpitSheet wip : wipList)
                            {
                                // 仓库发料
                                if(StringUtils.isBlank(wip.getProcessId()))
                                {
                                    dataList.add(wip);
                                }
                            }
                            data.put("type",1);
                            data.put("detailList",dataList);
                        }
                        else
                        {
                            Company company = new Company(factId);

                            // 初始时间处理
                            Date nowDate = new Date();

                            // 获取当日安排的任务
                            BottleneckProcessUse useQuery = new BottleneckProcessUse();
                            useQuery.setCompany(company);
                            useQuery.setOccurrenceDate(nowDate);
                            useQuery.setProcessManagementId(report.getProcessManagementId());
                            List<BottleneckProcessUse> useList = planSchedulingDao.processDetailsThreeA(useQuery);
                            data.put("useList",useList);

                            // 获取当日累计完成的瓶颈工序列表
                            useQuery.setDateType(1);
                            useQuery.setQueryDate(DateUtils.formatDate(useQuery.getOccurrenceDate()));
                            List<BottleneckProcessUse> tatalUseList = bottleneckProcessUseDao.getArrangeCompleteListThree(useQuery);
                            data.put("tatalUseList",tatalUseList);

                            data.put("type",3);
                        }

                    }
                    else
                    {
                        data.put("type",1);
                        data.put("detailList",wipList);
                    }
                }
                break;
        }
        return data;
    }
    //获取公司名称
    public Map<String,List<?>> getCompanyLists(Company company)
    {
        Map<String,List<?>> data = new HashMap<>();
        company.setPhone(company.getPhone());
        List<Company> companyLists = cockpitSheetDao.getCompanyLists(company);
        data.put("companyList",companyLists);
        List<Department> orgDepart = cockpitSheetDao.getOrgDepartLists(new Department());
        data.put("orgDepart",orgDepart);
        List<User> userList = cockpitSheetDao.getSalesLists(new User());
        data.put("userList",userList);
        return data;
    }
//    //获取部门名称
//    public Map<String,Object> getOrgDepartLists (Department department)
//    {
//        Map<String,Object>data = new HashMap<>();
//        if (department == null){
//            data.put("result", "fail");
//            data.put("message", "参数传递失败，请刷新重试!");
//            return data;
//        }
//        List<Department> orgDepart = cockpitSheetDao.getOrgDepartLists(department);
//        data.put("orgDepart",orgDepart);
//        return data;
//    }
//    //获取业务员
//    public Map<String,Object> getSalesLists(User user){
//        Map<String,Object>data = new HashMap<>();
//        if (user == null){
//            data.put("result","fail");
//            data.put("message","参数传递失败，请刷新重试");
//        }
//        List<User> userList = cockpitSheetDao.getSalesLists(user);
//        data.put("userList",userList);
//        return data;
//    }
    //获取员工和职位名称
    public Employee getEmployeeUserName(Employee employee){
        if (employee == null){
            return null;
        }
       Employee employeeObj = cockpitSheetDao.getEmployeeUserName(employee);
        return employeeObj;
    }

    // 微信进度
    public ContractDetail getProgress(ContractDetail contractDetail) {
        // 根据合同明细id获取创建人、创建时间、审批人、审批时间
        ContractDetail progress = contractDetailDao.getProgress(contractDetail);
        if (progress != null) {
            contractDetail.setContractDetailName(progress.getContractDetailName());
            contractDetail.setApprovalName(progress.getApprovalName());
            contractDetail.setCreatedDate(progress.getCreatedDate());
            contractDetail.setLastUpdDate(progress.getLastUpdDate());
        }

        String terminalDetailId = contractDetailDao.getTerminalDetailId(contractDetail);
        contractDetail.setRecordId(terminalDetailId);

        // 获取工程制卡和投料时间
        ContractDetail projectCard = contractDetailDao.getContractDetaliList(contractDetail);
        if (projectCard != null)
        {
            contractDetail.setCardName(projectCard.getCardName());
            contractDetail.setCardDate(projectCard.getCardDate());
            contractDetail.setFeedingName(projectCard.getFeedingName());
            contractDetail.setFeedingDate(projectCard.getFeedingDate());
        }

        // 获取过数工序
        ContractDetail process = contractDetailDao.getProcessList(contractDetail);
        if (process != null)
        {
            contractDetail.setProcessCategory(process.getProcessCategory());
            contractDetail.setCountName(process.getCountName());
            contractDetail.setCountDate(process.getCountDate());
        }
        return contractDetail;
    }

    // WIP进度
    public List<ProduceRecord> getWipProgress(ProduceRecord produceRecord)
    {
        // 根据批次id批次明细id获取交接板人交接板时间
        List<ProduceRecord> countPeopleList = produceRecordDao.geCountPeopleList(produceRecord);
        return countPeopleList;
    }

    public ReportTwo getHomePageReportTwo(Report report,HttpServletRequest request,
        HttpServletResponse response)
    {
/*        ReportTwo reportTwo = wechatOaDao.getHomeReportData(report);*/
        ReportTwo reportTwo = new ReportTwo();
        if(null == report || Collections3.isEmpty(report.getReportRoleList()))
        {
            return reportTwo;
        }
/*        Company company = new Company(CompanyUtil.getInstance().getFactId());
        String showDate = null;
        Calendar calendar = Calendar.getInstance();
        if(null != report.getDateType())
        {
            Date nowDate = new Date();
            switch (report.getDateType())
            {
                case 1:
                    showDate = DateUtils.formatDate(nowDate);
                    break;
                case 2:
                    int year = calendar.get(Calendar.YEAR);   // 获取年份
                    int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);   // 获取本年第几周
                    if(weekOfYear > 9)
                    {
                        showDate = year +""+ weekOfYear;
                    }
                    else
                    {
                        showDate = year +"0"+ weekOfYear;
                    }
                    break;
                case 3:
                    showDate = DateUtils.formatDate(nowDate,"yyyyMM");
                    break;
            }
        }*/
        String moduleId = null;
        if(Collections3.isNotEmpty(report.getReportRoleList()))
        {
            for(ReportRole role : report.getReportRoleList())
            {
                if(!"3".equals(role.getStatus()))
                {
                    continue;
                }
                String type = null;
                switch (role.getName())
                {
                    case "生产延期":
                        type = "1";
                        break;
                    case "生产风险":
                        type = "2";
                        break;
                    case "交期":
                        type = "3";
                        break;
                    case "过数":
                        type = "4";
                        break;
                }
                if(StringUtils.isNotBlank(type))
                {
                    if(StringUtils.isNotBlank(moduleId))
                    {
                        moduleId = moduleId +","+ type;
                    }
                    else
                    {
                        moduleId = type;
                    }
                }
            }
        }
        ReportException reprotExce = new ReportException();
        reprotExce.setDateType(report.getDateType());
        reprotExce.setModuleId(moduleId);
        List<ReportException> reportExceList = reportExceptionDao.getList(reprotExce);
        if(Collections3.isEmpty(reportExceList))
        {
            reportExceList = Lists.newArrayList();
        }
        else
        {
            reportExceList.sort((x, y) -> -(x.getType().compareTo(y.getType())));
        }

/*        // 获取生产进度
        Notification notification = new Notification();
        notification.setCompany(company);
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
        Map<String, Object> data = produceRecordService.selectWipAndDeal(notification);
        if(null != data)
        {
            if(null != data.get("list"))
            {
                List<Wip> wipList = (List<Wip>)data.get("list");
                if(Collections3.isNotEmpty(wipList))
                {
                    Integer postponedNum = 0;// 已延期
                    Integer riskyNumber = 0;// 有风险
                    for(Wip wip : wipList)
                    {
                        if(StringUtils.isNotBlank(wip.getState()))
                        {
                            if("存在风险".equals(wip.getState()))
                            {
                                riskyNumber++;
                            }
                            else if("进度延期".equals(wip.getState()))
                            {
                                postponedNum++;
                            }
                        }
                    }

                    ReportException obj1 = new ReportException();
                    obj1.setNum(postponedNum);
                    obj1.setType(1);
                    obj1.setShowDate(showDate);
                    reportExceList.add(obj1);

                    ReportException obj2 = new ReportException();
                    obj2.setNum(riskyNumber);
                    obj2.setType(2);
                    obj2.setShowDate(showDate);
                    reportExceList.add(obj2);
                }
            }
        }*/

        reportTwo.setReportExceList(reportExceList);

        // 工序监控分析
/*        BottleneckProcessUse use = new BottleneckProcessUse();
        use.setCompany(company);
        use.setCompanyId(company.getRecordId());
        use.setDateType(report.getDateType());
        Page<BottleneckProcessUse> page = productionBottleneckService.getBottleneckProcessUseList(new Page<BottleneckProcessUse>(request, response,-1),use);
        if(null != page && Collections3.isNotEmpty(page.getList()))
        {
            List<BottleneckProcessUse> list = Lists.newArrayList();
            for(BottleneckProcessUse bpUse : page.getList())
            {
                if(StringUtils.isNotBlank(bpUse.getProcessResult()) && "有责".equals(bpUse.getProcessResult()))
                {
                    list.add(bpUse);
                }
            }
            // 累计集合工序数据
            List<BottleneckProcessUse> overcountAnomalyList = Lists.newArrayList();
            if(Collections3.isNotEmpty(list))
            {
                for(BottleneckProcessUse useOne : list)
                {
                    Boolean flag = false;
                    if(Collections3.isNotEmpty(overcountAnomalyList))
                    {
                        for(BottleneckProcessUse over : overcountAnomalyList)
                        {
                            if(over.getProcessName().equals(useOne.getProcessName()))
                            {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if(flag)
                    {
                        continue;
                    }
                    BigDecimal useArea = BigDecimal.ZERO;
                    for(BottleneckProcessUse useTwo : list)
                    {
                        if(useOne.getProcessName().equals(useTwo.getProcessName()) && null != useTwo.getUseArea())
                        {
                            useArea = useArea.add(useTwo.getUseArea());
                        }
                    }
                    BottleneckProcessUse proUse = useOne.clone();
                    proUse.setUseArea(useArea);
                    overcountAnomalyList.add(proUse);
                }
            }
            reportTwo.setOvercountAnomalyList(overcountAnomalyList);
        }*/

/*
        // 获取报备数据
        SummaryPreparation sp = new SummaryPreparation();
        sp.setCompany(new Company("205"));
        sp.setDateType(report.getDateType());
        Page<SummaryPreparation> spPage = matPreparationService.summaryPage(new Page<SummaryPreparation>(request, response,-1), sp);
        if(null != spPage && Collections3.isNotEmpty(spPage.getList()))
        {
            BigDecimal preparationArea = BigDecimal.ZERO; // 备料面积
            BigDecimal preparationUseArea = BigDecimal.ZERO; // 备料使用面积
            BigDecimal preparationRemainArea = BigDecimal.ZERO; // 备料剩余面积
            for(SummaryPreparation pre : spPage.getList())
            {
                if(Collections3.isNotEmpty(pre.getList()))
                {
                    for(GroupOrgRelation org : pre.getList())
                    {
                        if(null != org.getDeptConfirmArea())
                        {
                            preparationArea = preparationArea.add(org.getDeptConfirmArea());
                        }
                    }
                }
                if(null != pre.getUseArea())
                {
                    preparationUseArea = preparationUseArea.add(pre.getUseArea());
                }
                preparationRemainArea = preparationArea.subtract(preparationUseArea);
            }
            reportTwo.setPreparationArea(preparationArea);
            reportTwo.setPreparationUseArea(preparationUseArea);
            reportTwo.setPreparationRemainArea(preparationRemainArea);
        }
*/
        return reportTwo;
    }

    @Transactional(readOnly = false)
    public void initReportExport(HttpServletRequest request, HttpServletResponse response)
    {
        Integer dateType = 3;
        Company company = new Company(CompanyUtil.getInstance().getFactId());
        List<ReportException> insertList = Lists.newArrayList();
        Date nowDate = new Date();

        // 工序监控分析
        BottleneckProcessUse use = new BottleneckProcessUse();
        use.setCompany(company);
        use.setCompanyId(company.getRecordId());
        use.setDateType(dateType);
        Page<BottleneckProcessUse> page = productionBottleneckService.getBottleneckProcessUseList(new Page<BottleneckProcessUse>(request, response,-1),use);
        if(null != page && Collections3.isNotEmpty(page.getList()))
        {
            List<BottleneckProcessUse> list = Lists.newArrayList();
            for(BottleneckProcessUse bpUse : page.getList())
            {
                if(StringUtils.isNotBlank(bpUse.getProcessResult()) && "有责".equals(bpUse.getProcessResult()))
                {
                    list.add(bpUse);
                }
            }
            if(Collections3.isNotEmpty(list))
            {
                for(BottleneckProcessUse botUse : list)
                {
                    ReportException obj = null;
                    if(Collections3.isNotEmpty(insertList))
                    {
                        for(ReportException exp : insertList)
                        {
                            if(exp.getType().compareTo(4) == 0 && exp.getDayStr().equals(botUse.getOccurrenceDateStr()))
                            {
                                obj = exp;
                                break;
                            }
                        }
                    }
                    if(null != obj)
                    {
                        obj.setNum(obj.getNum() + 1);
                    }
                    else
                    {
                        obj = new ReportException();
                        obj.setDay(botUse.getOccurrenceDate());
                        obj.setNum(1);
                        obj.setType(4);
                        obj.setCreatedDate(nowDate);
                        insertList.add(obj);
                    }
                }
            }
        }

        ContractDetail query = new ContractDetail();
        query.setDateType(dateType);
        List<ContractDetail> list = contractDetailDao.getDeliveryMonitoringTwoList(query);
        if(Collections3.isNotEmpty(list))
        {
            for(ContractDetail detail : list)
            {
                if((null != detail.getEstimateDate() && detail.getEstimateDate().compareTo(detail.getDeliveryDate()) > 0)
                || (null == detail.getEstimateDate() && !detail.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    && new Date().compareTo(detail.getDeliveryDate()) > 0))
                {
                    ReportException obj = null;
                    if(Collections3.isNotEmpty(insertList))
                    {
                        for(ReportException exp : insertList)
                        {
                            if(exp.getType().compareTo(3) == 0 && exp.getDayStr().equals(detail.getEstimateStartDate()))
                            {
                                obj = exp;
                                break;
                            }
                        }
                    }
                    if(null != obj)
                    {
                        obj.setNum(obj.getNum() + 1);
                    }
                    else
                    {
                        obj = new ReportException();
                        obj.setDay(DateUtils.parseDate(detail.getEstimateStartDate()));
                        obj.setNum(1);
                        obj.setType(3);
                        obj.setCreatedDate(nowDate);
                        insertList.add(obj);
                    }
                }
            }
        }
        // 清除
        ReportException exp = new ReportException();
        exp.setDateType(3);
        reportExceptionDao.cleanData(exp);
        // 添加
        if(Collections3.isNotEmpty(insertList))
        {
            reportExceptionDao.batchInsert(insertList);
        }
    }

    public List<Object> getShowDateData(ReportException reportException, HttpServletRequest request,
        HttpServletResponse response)
    {
        List<Object> objectList = Lists.newArrayList();
        if(null == reportException || null == reportException.getType())
        {
            return null;
        }
        Company company = new Company(CompanyUtil.getInstance().getFactId());
        switch (reportException.getType())
        {
            case 1:
                // 获取生产进度
                Notification notification = new Notification();
                notification.setCompany(company);
                notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
                notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
                Map<String, Object> data = produceRecordService.selectWipAndDeal(notification);
                if(null != data)
                {
                    if(null != data.get("list"))
                    {
                        List<Wip> wipList = (List<Wip>)data.get("list");
                        if(Collections3.isNotEmpty(wipList))
                        {
                            for(Wip wip : wipList)
                            {
                                if(StringUtils.isNotBlank(wip.getState()))
                                {
                                    if("进度延期".equals(wip.getState()))
                                    {
                                        objectList.add(wip);
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            case 2:
                // 获取生产进度
                notification = new Notification();
                notification.setCompany(company);
                notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
                notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
                data = produceRecordService.selectWipAndDeal(notification);
                if(null != data)
                {
                    if(null != data.get("list"))
                    {
                        List<Wip> wipList = (List<Wip>)data.get("list");
                        if(Collections3.isNotEmpty(wipList))
                        {
                            for(Wip wip : wipList)
                            {
                                if(StringUtils.isNotBlank(wip.getState()))
                                {
                                    if("存在风险".equals(wip.getState()))
                                    {
                                        objectList.add(wip);
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            case 3:
                ContractDetail query = new ContractDetail();
                query.setDateType(reportException.getDateType());
                List<ContractDetail> list = contractDetailDao.getDeliveryMonitoringTwoList(query);
                if(Collections3.isNotEmpty(list))
                {
                    List<ContractDetail> detailList = Lists.newArrayList();
                    String detailIds = null;
                    for(ContractDetail detail : list)
                    {
                        if((null != detail.getEstimateDate() && detail.getEstimateDate().compareTo(detail.getDeliveryDate()) > 0)
                            || (null == detail.getEstimateDate() && !detail.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                            && new Date().compareTo(detail.getDeliveryDate()) > 0))
                        {
                            if(StringUtils.isNotBlank(detailIds))
                            {
                                detailIds = detailIds +","+ detail.getRecordId();
                            }
                            else
                            {
                                detailIds = detail.getRecordId();
                            }
                            detailList.add(detail);
                        }
                    }
                    if(StringUtils.isNotBlank(detailIds))
                    {
                        // 获取分配交货列表
                        List<BatchDelivery> batchDeliveryList = batchDeliveryDao.getBatchDeliveryList(detailIds);
                        // 获取产能分配列表
                        List<CapacityDeail> capaList = capacityDao.getListByDetailId(detailIds);
                        for(ContractDetail detail : detailList)
                        {
                            detail.setCapacityDeailList(Lists.newArrayList());
                            detail.setBatchList(Lists.newArrayList());
                            if(Collections3.isNotEmpty(batchDeliveryList))
                            {
                                for(BatchDelivery bd : batchDeliveryList)
                                {
                                    if(StringUtils.isNotBlank(bd.getConDetId()) && bd.getConDetId().equals(detail.getRecordId()))
                                    {
                                        detail.getBatchList().add(bd);
                                    }
                                }
                            }
                            if(Collections3.isNotEmpty(capaList))
                            {
                                for(CapacityDeail cd : capaList)
                                {
                                    if(StringUtils.isNotBlank(cd.getContactDeailId()) && cd.getContactDeailId().equals(detail.getRecordId()))
                                    {
                                        detail.getCapacityDeailList().add(cd);
                                    }
                                }
                            }
                            if(Collections3.isNotEmpty(detail.getBatchList()))
                            {
                                detail.getBatchList().sort((x, y) -> ((null == x.getDeliveryDate() ? new Date() : x.getDeliveryDate())
                                    .compareTo(null == y.getDeliveryDate() ? new Date() : y.getDeliveryDate())));
                                detail.setFirstBatchDate(detail.getBatchList().get(0).getDeliveryDate());
                                detail.setFinallyBatchDate(detail.getBatchList().get(detail.getBatchList().size() - 1).getDeliveryDate());
                            }
                            if(Collections3.isNotEmpty(detail.getCapacityDeailList()))
                            {
                                detail.getCapacityDeailList().sort((x, y) -> ((null == x.getProTime() ? new Date() : x.getProTime())
                                    .compareTo(null == y.getProTime() ? new Date() : y.getProTime())));
                                detail.setCapacityFirstBatchDate(detail.getCapacityDeailList().get(0).getProTime());
                                detail.setCapacityFinallyBatchDate(detail.getCapacityDeailList().get(detail.getCapacityDeailList().size() - 1).getProTime());
                            }
                        }
                    }
                    detailList.sort((x, y) -> -(( StringUtils.isBlank(x.getEstimateStartDate())  ? "0" : x.getEstimateStartDate())
                        .compareTo(StringUtils.isBlank(y.getEstimateStartDate()) ? "0" : y.getEstimateStartDate())));
                    objectList.addAll(detailList);
                }
                break;
            case 4:
                // 工序监控分析
                BottleneckProcessUse use = new BottleneckProcessUse();
                use.setCompany(company);
                use.setCompanyId(company.getRecordId());
                use.setDateType(reportException.getDateType());
                Page<BottleneckProcessUse> page = productionBottleneckService.getBottleneckProcessUseList(new Page<BottleneckProcessUse>(request, response,-1),use);
                if(null != page && Collections3.isNotEmpty(page.getList()))
                {
                    List<BottleneckProcessUse> useList = Lists.newArrayList();
                    for(BottleneckProcessUse bpUse : page.getList())
                    {
                        if(StringUtils.isNotBlank(bpUse.getProcessResult()) && "有责".equals(bpUse.getProcessResult()))
                        {
                            useList.add(bpUse);
                        }
                    }
                    if(Collections3.isNotEmpty(useList))
                    {
                        useList.sort((x, y) -> -(( null == x.getTakeOverTime()  ? new Date() : x.getTakeOverTime())
                            .compareTo(null == y.getTakeOverTime() ? new Date() : y.getTakeOverTime())));
                        objectList.addAll(useList);
                    }
                }
                break;
        }
        return objectList;
    }

    public Map<String, Object>  getOrderReportStatus(Report report)
    {
        Map<String, Object> data = new HashMap<>();
        report.setQueryType("2");
        List<Cockpit> orderLineList = cockpitDao.getOrderLineData(report);
        if(Collections3.isNotEmpty(orderLineList))
        {
            // 获取正常、风险、延期集合
            getNormalRiskListTwo(data, orderLineList,report.getCompareDateFlag());
        }
        return data;
    }
}
