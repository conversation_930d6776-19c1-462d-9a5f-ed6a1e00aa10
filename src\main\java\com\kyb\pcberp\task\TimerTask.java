package com.kyb.pcberp.task;

import com.kyb.pcberp.task.util.TaskUtil;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class TimerTask
{
/*    // 每日0点
    @Scheduled(cron = "0 00 00 * * ?")
    @Transactional(readOnly = false)
    public void ZeroRun()
    {
        int open = 1;
        int close = 0;
        TaskUtil.getInstance().runData(open, open, open, close, close, open, close, open, open,open);
    }

    // 每月1号
    @Scheduled(cron = "0 01 01 1 * ?")
    @Transactional(readOnly = false)
    public void Month1StRun()
    {
        int open = 1;
        int close = 0;
        TaskUtil.getInstance().runData(close, close, close, open, open, close, close, close, close,close);
    }

    // 每日7点50分
    @Scheduled(cron = "0 50 07 * * ?")
    @Transactional(readOnly = false)
    public void ZeroRunTwo()
    {
        int open = 1;
        int close = 0;
        TaskUtil.getInstance().runData(close, close, close, close, close, close, open, close, close,close);
    }*/
}
